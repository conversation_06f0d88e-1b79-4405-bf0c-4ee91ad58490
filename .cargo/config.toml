[alias]
vdev = "run --quiet --package vdev --"

[env]
# Build with large pages so that Vector runs on systems with 64k pages or less (e.g. 4k) to support
# CentOS 7, 8, and a few other Linux distributions.
JEMALLOC_SYS_WITH_LG_PAGE = "16"

[target.'cfg(all())']
rustflags = [
  "-Dclippy::print_stdout",
  "-Dclippy::print_stderr",
  "-Dclippy::dbg_macro",
]

# We need to bring in `libstdc++` for things that build against C++ (librdkafka, etc) which comes along in the
# `cross` base image but _isn't_ in a path searched by the linker normally. Additionally, our custom Docker image that
# we base on the `cross` image moves `libstdc++` into this custom-looking directory to avoid some _other_ libraries
# included in the `cross` base image from having a higher precedence than some of the "self-contained" libraries that
# Rust will bundle/use for specific targets like MUSL.
[target.x86_64-unknown-linux-musl]
rustflags = "-Lnative=/lib/native-libs"

[target.aarch64-unknown-linux-musl]
rustflags = "-Lnative=/lib/native-libs"

[target.armv7-unknown-linux-musleabihf]
rustflags = "-Lnative=/lib/native-libs"

[target.x86_64-unknown-linux-gnu]
rustflags = ["-C", "link-args=-rdynamic"]

[target.aarch64-unknown-linux-gnu]
rustflags = ["-C", "link-args=-rdynamic"]

[target.x86_64-pc-windows-msvc]
# https://github.com/dtolnay/inventory/issues/58
rustflags = ["-C", "codegen-units=1"]
