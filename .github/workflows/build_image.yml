name: build_image

permissions:
  id-token: write
  contents: read

on:
  workflow_dispatch:

env:
  CARGO_TERM_COLOR: always
  RUST_BACKTRACE: 1

jobs:
  generate-build-meta:
    name: Generate Build Meta
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v2
    - name: Generate Build Meta
      id: build_meta
      run: |
        echo "vector_build_desc=$(git rev-parse --short HEAD) $(date +%Y-%m-%d)" >> $GITHUB_OUTPUT
        echo "vector_build_sha=$(git rev-parse HEAD)" >> $GITHUB_OUTPUT
    outputs:
      vector_build_desc: ${{ steps.build_meta.outputs.vector_build_desc }}
      vector_build_sha: ${{ steps.build_meta.outputs.vector_build_sha }}

  build-x86_64-unknown-linux-gnu-binary:
    name: Build x86_64-unknown-linux-gnu
    runs-on: ubuntu-latest
    needs:
      - generate-build-meta
    env:
      VECTOR_BUILD_DESC: ${{ needs.generate-build-meta.outputs.vector_build_desc }}
      VECTOR_BUILD_SHA: ${{ needs.generate-build-meta.outputs.vector_build_sha }}
    steps:
    - uses: actions/checkout@v2
    - name: Set up toolchains
      uses: actions-rs/toolchain@v1
    - name: Install cross
      run: cargo install cross
    - name: Build x86_64-unknown-linux-gnu
      run: make build-x86_64-unknown-linux-gnu
    - name: Upload x86_64-unknown-linux-gnu
      uses: actions/upload-artifact@v2
      with:
        name: vector-${{ env.VECTOR_BUILD_SHA }}-x86_64-unknown-linux-gnu
        path: target/x86_64-unknown-linux-gnu/release/vector

  build-aarch64-unknown-linux-gnu-binary:
    name: Build aarch64-unknown-linux-gnu
    runs-on: ubuntu-latest
    needs:
      - generate-build-meta
    env:
      VECTOR_BUILD_DESC: ${{ needs.generate-build-meta.outputs.vector_build_desc }}
      VECTOR_BUILD_SHA: ${{ needs.generate-build-meta.outputs.vector_build_sha }}
      JEMALLOC_SYS_WITH_LG_PAGE: 16
    steps:
    - uses: actions/checkout@v2
    - name: Set up toolchains
      uses: actions-rs/toolchain@v1
    - name: Install cross
      run: cargo install cross
    - name: Build aarch64-unknown-linux-gnu
      run: make build-aarch64-unknown-linux-gnu
    - name: Upload aarch64-unknown-linux-gnu
      uses: actions/upload-artifact@v2
      with:
        name: vector-${{ env.VECTOR_BUILD_SHA }}-aarch64-unknown-linux-gnu
        path: target/aarch64-unknown-linux-gnu/release/vector

  build-armv7-unknown-linux-gnueabihf-binary:
    name: Build armv7-unknown-linux-gnueabihf
    runs-on: ubuntu-latest
    needs:
      - generate-build-meta
    env:
      VECTOR_BUILD_DESC: ${{ needs.generate-build-meta.outputs.vector_build_desc }}
      VECTOR_BUILD_SHA: ${{ needs.generate-build-meta.outputs.vector_build_sha }}
      JEMALLOC_SYS_WITH_LG_PAGE: 16
    steps:
    - uses: actions/checkout@v2
    - name: Set up toolchains
      uses: actions-rs/toolchain@v1
    - name: Install cross
      run: cargo install cross
    - name: Build armv7-unknown-linux-gnueabihf
      run: make build-armv7-unknown-linux-gnueabihf
    - name: Upload armv7-unknown-linux-gnueabihf
      uses: actions/upload-artifact@v2
      with:
        name: vector-${{ env.VECTOR_BUILD_SHA }}-armv7-unknown-linux-gnueabihf
        path: target/armv7-unknown-linux-gnueabihf/release/vector

  push-image:
    name: Push Docker Image
    runs-on: ubuntu-latest
    needs:
      - generate-build-meta
      - build-x86_64-unknown-linux-gnu-binary
      - build-aarch64-unknown-linux-gnu-binary
      - build-armv7-unknown-linux-gnueabihf-binary
    env:
      VECTOR_BUILD_DESC: ${{ needs.generate-build-meta.outputs.vector_build_desc }}
      VECTOR_BUILD_SHA: ${{ needs.generate-build-meta.outputs.vector_build_sha }}
    steps:
    - uses: actions/checkout@v2
    - name: Set up QEMU
      uses: docker/setup-qemu-action@v2
    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v2
    - name: Configure AWS Credentials for DBaaS Dev
      uses: aws-actions/configure-aws-credentials@v1
      with:
        role-to-assume: arn:aws:iam::385595570414:role/vector-cicd
        aws-region: us-west-2
    - name: Download staged binary (x86_64-unknown-linux-gnu)
      uses: actions/download-artifact@v2
      with:
        name: vector-${{ env.VECTOR_BUILD_SHA }}-x86_64-unknown-linux-gnu
        path: target/x86_64-unknown-linux-gnu/release
    - name: Download staged binary (aarch64-unknown-linux-gnu)
      uses: actions/download-artifact@v2
      with:
        name: vector-${{ env.VECTOR_BUILD_SHA }}-aarch64-unknown-linux-gnu
        path: target/aarch64-unknown-linux-gnu/release
    - name: Download staged binary (armv7-unknown-linux-gnueabihf)
      uses: actions/download-artifact@v2
      with:
        name: vector-${{ env.VECTOR_BUILD_SHA }}-armv7-unknown-linux-gnueabihf
        path: target/armv7-unknown-linux-gnueabihf/release
    - name: Set Release Meta
      run: |
        echo "TAG=385595570414.dkr.ecr.us-west-2.amazonaws.com/tidbcloud/vector:nightly-$(git rev-parse --short HEAD)" >> $GITHUB_ENV
    - name: Push to DBaaS Dev ECR
      run: |
        aws ecr get-login-password --region us-west-2 | docker login --username AWS --password-stdin 385595570414.dkr.ecr.us-west-2.amazonaws.com
        make release-docker
