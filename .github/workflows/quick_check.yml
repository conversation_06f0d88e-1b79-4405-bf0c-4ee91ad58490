name: quick_check

on:
  pull_request:
    branches: [ master ]

env:
  CARGO_TERM_COLOR: always

jobs:
  check:
    runs-on: ubuntu-latest
    env:
      RUST_BACKTRACE: 1
      RUSTFLAGS: "-D warnings"
    steps:
    - uses: actions/checkout@v2

    - uses: actions/cache@v2
      with:
        path: |
          ~/.cargo/registry
          ~/.cargo/git
          target
        key: ${{ runner.os }}-cargo-quick_check-${{ hashFiles('**/Cargo.lock') }}
        restore-keys: |
          ${{ runner.os }}-cargo-quick_check-${{ hashFiles('**/Cargo.lock') }}
          ${{ runner.os }}-cargo-quick_check-
          ${{ runner.os }}-cargo-

    - name: Set up toolchains
      uses: actions-rs/toolchain@v1
      with:
          components: rustfmt, clippy

    - name: Check format
      run: cargo fmt --all -- --check

    - name: Lint
      run: make clippy

    - name: Test
      run: make test
