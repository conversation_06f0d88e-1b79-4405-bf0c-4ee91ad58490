[package]
name = "vector-extensions"
version = "0.49.0"
edition = "2021"
readme = "README.md"
publish = false
default-run = "vector"

[[bin]]
name = "vector"
path = "src/main.rs"

[dependencies]
async-recursion = "1.1.1"
async-trait = { version = "0.1.88", default-features = false }
arrow = { version = "55.2.0" }
aws-sdk-s3 = { version = "1.82.0", default-features = false, features = ["behavior-version-latest"] }
aws-smithy-types = { version = "1.3.2", default-features = false }
azure_storage_blobs = { version = "0.17.0", default-features = false, features = ["enable_reqwest"] }
base64 = { version = "0.22.1", default-features = false }
bytes = { version = "1.10.1", default-features = false, features = ["serde"] }
chrono = { version = "0.4.41", default-features = false, features = ["clock", "serde"] }
deltalake = { version = "0.27", features = ["datafusion"] }
datafusion = { version = "48"}
etcd-client = { version = "0.14", features = ["tls-roots"] }
exitcode = { version = "1.1.2", default-features = false }
file-source = { git = "https://github.com/vectordotdev/vector", tag = "v0.49.0" }
flate2 = { version = "1.1.2", default-features = false, features = ["default"] }
futures = { version = "0.3.31", default-features = false, features = ["compat", "io-compat"], package = "futures" }
futures-util = { version = "0.3.29", default-features = false }
goauth = { version = "0.16.0" }
hex = { version = "0.4.3", default-features = false }
http = { version = "0.2.9", default-features = false }
hyper = { version = "0.14.28", default-features = false, features = ["client", "runtime", "http1", "http2", "server", "stream"] }
inventory = { version = "0.3.20", default-features = false }
k8s-openapi = { version = "0.25.0", features = ["latest"] }
kube = { version = "1.0.0" }
md-5 = { version = "0.10", default-features = false }
metrics = "0.24.2"
ordered-float = { version = "4.6.0", default-features = false }
parquet = { version = "55.2.0" }
prost = { version = "0.12", default-features = false, features = ["std"] }
prost-types = { version = "0.12", default-features = false }
rand = "0.9.2"
reqwest = { version = "0.11", features = ["native-tls"] }
serde = { version = "1.0.219", default-features = false, features = ["derive"] }
serde_json = { version = "1.0.142", default-features = false, features = ["std", "raw_value"] }
snafu = { version = "0.8.6", default-features = false, features = ["futures"] }
sqlx = { version = "0.8", features = ["mysql", "runtime-tokio-rustls", "chrono"] }
tokio = { version = "1.45.1", default-features = false, features = ["full"] }
tokio-openssl = { version = "0.6.5", default-features = false }
tokio-stream = { version = "0.1.17", default-features = false, features = ["net", "sync", "time"] }
tokio-util = { version = "0.7", default-features = false, features = ["io", "time"] }
toml = { version = "0.9.4", default-features = false }
tonic = { version = "0.11", default-features = false, features = ["transport", "codegen", "prost", "tls", "tls-roots", "gzip"] }
tracing = { version = "0.1.34", default-features = false }
tracing-futures = { version = "0.2.5", default-features = false, features = ["futures-03"] }
typetag = { version = "0.2.20", default-features = false }
url = { version = "2.5.4", default-features = false, features = ["serde"] }
vector = { git = "https://github.com/vectordotdev/vector", tag = "v0.49.0", default-features = false, features = ["aws-config", "sinks-aws_s3", "gcp", "sinks-gcp"] }
vector-config = { git = "https://github.com/vectordotdev/vector", tag = "v0.49.0", default-features = false }
vector-lib = { git = "https://github.com/vectordotdev/vector", tag = "v0.49.0", default-features = false }

 

[dev-dependencies]
lazy_static = "1.4.0"
regex = "1.10.3"

[build-dependencies]
prost-build = { version = "0.12", default-features = false }
tonic-build = { version = "0.11", default-features = false, features = ["transport", "prost"] }

[features]
default = [
    "vector/api",
    "vector/api-client",
    "vector/enrichment-tables",
    "vector/sources-dnstap",
    "vector/transforms",
    "vector/unix",
    "vector/secrets",
    "sources",
    "sinks",
]
sources = ["sources-logs", "vector/sources-metrics"]
# Enable nextgen functionality for TiDB/TiKV
nextgen = []
sources-logs = [
  "vector/sources-amqp",
  "vector/sources-aws_kinesis_firehose",
  "vector/sources-aws_s3",
  "vector/sources-aws_sqs",
  "vector/sources-datadog_agent",
  "vector/sources-demo_logs",
  "vector/sources-docker_logs",
  "vector/sources-exec",
  "vector/sources-file",
  "vector/sources-fluent",
  "vector/sources-gcp_pubsub",
  "vector/sources-heroku_logs",
  "vector/sources-http_server",
  "vector/sources-http_client",
  "vector/sources-internal_logs",
  "vector/sources-journald",
  # "vector/sources-kafka",
  "vector/sources-kubernetes_logs",
  "vector/sources-logstash",
  "vector/sources-mqtt",
  "vector/sources-nats",
  "vector/sources-opentelemetry",
  "vector/sources-pulsar",
  "vector/sources-file_descriptor",
  "vector/sources-redis",
  "vector/sources-socket",
  "vector/sources-splunk_hec",
  "vector/sources-stdin",
  "vector/sources-syslog",
  "vector/sources-vector",
  "vector/sources-websocket",
]
sinks = ["sinks-logs", "sinks-metrics"]
sinks-logs = [
  "vector/sinks-amqp",
  "vector/sinks-appsignal",
  "vector/sinks-aws_cloudwatch_logs",
  "vector/sinks-aws_kinesis_firehose",
  "vector/sinks-aws_kinesis_streams",
  "vector/sinks-aws_s3",
  "vector/sinks-aws_sns",
  "vector/sinks-aws_sqs",
  "vector/sinks-axiom",
  "vector/sinks-azure_blob",
  "vector/sinks-azure_monitor_logs",
  "vector/sinks-blackhole",
  "vector/sinks-chronicle",
  "vector/sinks-clickhouse",
  "vector/sinks-console",
  "vector/sinks-databend",
  "vector/sinks-datadog_events",
  "vector/sinks-datadog_logs",
  "vector/sinks-datadog_traces",
  "vector/sinks-elasticsearch",
  "vector/sinks-file",
  "vector/sinks-gcp",
  "vector/sinks-greptimedb_logs",
  "vector/sinks-honeycomb",
  "vector/sinks-http",
  "vector/sinks-humio",
  "vector/sinks-influxdb",
#   "vector/sinks-kafka",
  "vector/sinks-keep",
  "vector/sinks-loki",
  "vector/sinks-mezmo",
  "vector/sinks-mqtt",
  "vector/sinks-nats",
  "vector/sinks-new_relic",
  "vector/sinks-new_relic_logs",
  "vector/sinks-opentelemetry",
  "vector/sinks-papertrail",
  "vector/sinks-postgres",
  "vector/sinks-pulsar",
  "vector/sinks-redis",
  "vector/sinks-sematext",
  "vector/sinks-socket",
  "vector/sinks-splunk_hec",
  "vector/sinks-vector",
  "vector/sinks-webhdfs",
  "vector/sinks-websocket",
  "vector/sinks-websocket-server",
]
sinks-metrics = [
  "vector/sinks-appsignal",
  "vector/sinks-aws_cloudwatch_metrics",
  "vector/sinks-blackhole",
  "vector/sinks-console",
  "vector/sinks-datadog_metrics",
  "vector/sinks-greptimedb_metrics",
  "vector/sinks-humio",
  "vector/sinks-influxdb",
#   "vector/sinks-kafka",
  "vector/sinks-prometheus",
  "vector/sinks-sematext",
  "vector/sinks-statsd",
  "vector/sinks-vector",
  "vector/sinks-splunk_hec"
]

[patch.crates-io]
async-compression = { git = "https://github.com/nolouch/async-compression", rev = "ba69fdc" }