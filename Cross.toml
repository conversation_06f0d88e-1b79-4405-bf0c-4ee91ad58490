[build.env]
passthrough = [
    "BUILD_DIR",
    "CARGO_INCREMENTAL",
    "CARGO_PROFILE_RELEASE_OPT_LEVEL",
    "CARGO_PROFILE_RELEASE_CODEGEN_UNITS",
    "RUST_BACKTRACE",
    "RUST_LOG",
    "VECTOR_BUILD_DESC",
    "JEMALLOC_SYS_WITH_LG_PAGE",
    "JEMALLOC_SYS_WITH_LG_HUGEPAGE",
]

[target.x86_64-unknown-linux-gnu]
image = "vector-cross-env:x86_64-unknown-linux-gnu"

[target.aarch64-unknown-linux-gnu]
image = "vector-cross-env:aarch64-unknown-linux-gnu"

[target.x86_64-unknown-linux-musl]
image = "vector-cross-env:x86_64-unknown-linux-musl"

[target.aarch64-unknown-linux-musl]
image = "vector-cross-env:aarch64-unknown-linux-musl"

[target.armv7-unknown-linux-gnueabihf]
image = "vector-cross-env:armv7-unknown-linux-gnueabihf"

[target.armv7-unknown-linux-musleabihf]
image = "vector-cross-env:armv7-unknown-linux-musleabihf"

[target.arm-unknown-linux-gnueabi]
image = "vector-cross-env:arm-unknown-linux-gnueabi"

[target.arm-unknown-linux-musleabi]
image = "vector-cross-env:arm-unknown-linux-musleabi"
