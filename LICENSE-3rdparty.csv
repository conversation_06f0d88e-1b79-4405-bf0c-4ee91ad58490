Component,Origin,License,Copyright
Vector,https://github.com/vectordotdev/vector,MPL-2.0,The Vector Authors
Inflector,https://github.com/whatisinternet/inflector,BSD-2-<PERSON><PERSON>,<PERSON><josh<PERSON><EMAIL>>
RustyXML,https://github.com/Florob/RustyXML,MIT OR Apache-2.0,<PERSON><PERSON><PERSON> <<EMAIL>>
addr2line,https://github.com/gimli-rs/addr2line,Apache-2.0 OR MIT,The addr2line Authors
adler,https://github.com/jonas-schievink/adler,0BSD OR MIT OR Apache-2.0,<PERSON> <<EMAIL>>
adler32,https://github.com/remram44/adler32-rs,<PERSON>li<PERSON>,<PERSON><PERSON> <<EMAIL>>
aead,https://github.com/RustCrypto/traits,MIT OR Apache-2.0,RustCrypto Developers
aes,https://github.com/RustCrypto/block-ciphers,MIT OR Apache-2.0,RustCrypto Developers
ahash,https://github.com/tkaitchuck/ahash,MIT OR Apache-2.0,<PERSON> Kaitchuck <<EMAIL>>
aho-corasick,https://github.com/BurntSushi/aho-corasick,Unlicense OR MIT,Andrew Gallant <<EMAIL>>
allocator-api2,https://github.com/zakarumych/allocator-api2,MIT OR Apache-2.0,Zakarum <<EMAIL>>
amq-protocol,https://github.com/amqp-rs/amq-protocol,BSD-2-Clause,Marc-Antoine Perennou <%<EMAIL>>
android-tzdata,https://github.com/RumovZ/android-tzdata,MIT OR Apache-2.0,RumovZ
android_system_properties,https://github.com/nical/android_system_properties,MIT OR Apache-2.0,Nicolas Silva <<EMAIL>>
ansi_term,https://github.com/ogham/rust-ansi-term,MIT,"<EMAIL>, Ryan Scheel (Havvy) <<EMAIL>>, Josh Triplett <<EMAIL>>"
anstream,https://github.com/rust-cli/anstyle,MIT OR Apache-2.0,The anstream Authors
anstyle,https://github.com/rust-cli/anstyle,MIT OR Apache-2.0,The anstyle Authors
anstyle-parse,https://github.com/rust-cli/anstyle,MIT OR Apache-2.0,The anstyle-parse Authors
anstyle-query,https://github.com/rust-cli/anstyle,MIT OR Apache-2.0,The anstyle-query Authors
anstyle-wincon,https://github.com/rust-cli/anstyle,MIT OR Apache-2.0,The anstyle-wincon Authors
anyhow,https://github.com/dtolnay/anyhow,MIT OR Apache-2.0,David Tolnay <<EMAIL>>
apache-avro,https://github.com/apache/avro,Apache-2.0,Apache Avro team <<EMAIL>>
arbitrary,https://github.com/rust-fuzz/arbitrary,MIT OR Apache-2.0,"The Rust-Fuzz Project Developers, Nick Fitzgerald <<EMAIL>>, Manish Goregaokar <<EMAIL>>, Simonas Kazlauskas <<EMAIL>>, Brian L. Troutwine <<EMAIL>>, Corey Farwell <<EMAIL>>"
arc-swap,https://github.com/vorner/arc-swap,MIT OR Apache-2.0,Michal 'vorner' Vaner <<EMAIL>>
arr_macro,https://github.com/JoshMcguigan/arr_macro,MIT OR Apache-2.0,Josh Mcguigan
arrayvec,https://github.com/bluss/arrayvec,MIT OR Apache-2.0,bluss
ascii,https://github.com/tomprogrammer/rust-ascii,Apache-2.0  OR  MIT,"Thomas Bahn <<EMAIL>>, Torbjørn Birch Moltu <<EMAIL>>, Simon Sapin <<EMAIL>>"
async-channel,https://github.com/smol-rs/async-channel,Apache-2.0 OR MIT,Stjepan Glavina <<EMAIL>>
async-compression,https://github.com/Nullus157/async-compression,MIT OR Apache-2.0,"Wim Looman <<EMAIL>>, Allen Bui <<EMAIL>>"
async-executor,https://github.com/smol-rs/async-executor,Apache-2.0 OR MIT,Stjepan Glavina <<EMAIL>>
async-fs,https://github.com/smol-rs/async-fs,Apache-2.0 OR MIT,Stjepan Glavina <<EMAIL>>
async-global-executor,https://github.com/Keruspe/async-global-executor,Apache-2.0 OR MIT,Marc-Antoine Perennou <<EMAIL>>
async-graphql,https://github.com/async-graphql/async-graphql,MIT OR Apache-2.0,"sunli <<EMAIL>>, Koxiaet"
async-io,https://github.com/smol-rs/async-io,Apache-2.0 OR MIT,Stjepan Glavina <<EMAIL>>
async-lock,https://github.com/smol-rs/async-lock,Apache-2.0 OR MIT,Stjepan Glavina <<EMAIL>>
async-nats,https://github.com/nats-io/nats.rs,Apache-2.0,"Tomasz Pietrek <<EMAIL>>, Casper Beyer <<EMAIL>>"
async-net,https://github.com/smol-rs/async-net,Apache-2.0 OR MIT,Stjepan Glavina <<EMAIL>>
async-process,https://github.com/smol-rs/async-process,Apache-2.0 OR MIT,Stjepan Glavina <<EMAIL>>
async-reactor-trait,https://github.com/amqp-rs/reactor-trait,Apache-2.0 OR MIT,Marc-Antoine Perennou <<EMAIL>>
async-recursion,https://github.com/dcchut/async-recursion,MIT OR Apache-2.0,Robert Usher <<EMAIL>>
async-signal,https://github.com/smol-rs/async-signal,Apache-2.0 OR MIT,John Nunley <<EMAIL>>
async-stream,https://github.com/tokio-rs/async-stream,MIT,Carl Lerche <<EMAIL>>
async-task,https://github.com/smol-rs/async-task,Apache-2.0 OR MIT,Stjepan Glavina <<EMAIL>>
async-trait,https://github.com/dtolnay/async-trait,MIT OR Apache-2.0,David Tolnay <<EMAIL>>
atomic,https://github.com/Amanieu/atomic-rs,Apache-2.0 OR MIT,Amanieu d'Antras <<EMAIL>>
atomic-waker,https://github.com/smol-rs/atomic-waker,Apache-2.0 OR MIT,"Stjepan Glavina <<EMAIL>>, Contributors to futures-rs"
atty,https://github.com/softprops/atty,MIT,softprops <<EMAIL>>
aws-config,https://github.com/smithy-lang/smithy-rs,Apache-2.0,"AWS Rust SDK Team <<EMAIL>>, Russell Cohen <<EMAIL>>"
aws-credential-types,https://github.com/smithy-lang/smithy-rs,Apache-2.0,AWS Rust SDK Team <<EMAIL>>
aws-http,https://github.com/smithy-lang/smithy-rs,Apache-2.0,"AWS Rust SDK Team <<EMAIL>>, Russell Cohen <<EMAIL>>"
aws-runtime,https://github.com/smithy-lang/smithy-rs,Apache-2.0,AWS Rust SDK Team <<EMAIL>>
aws-sdk-cloudwatch,https://github.com/awslabs/aws-sdk-rust,Apache-2.0,"AWS Rust SDK Team <<EMAIL>>, Russell Cohen <<EMAIL>>"
aws-sdk-cloudwatchlogs,https://github.com/awslabs/aws-sdk-rust,Apache-2.0,"AWS Rust SDK Team <<EMAIL>>, Russell Cohen <<EMAIL>>"
aws-sdk-firehose,https://github.com/awslabs/aws-sdk-rust,Apache-2.0,"AWS Rust SDK Team <<EMAIL>>, Russell Cohen <<EMAIL>>"
aws-sdk-kinesis,https://github.com/awslabs/aws-sdk-rust,Apache-2.0,"AWS Rust SDK Team <<EMAIL>>, Russell Cohen <<EMAIL>>"
aws-sdk-s3,https://github.com/awslabs/aws-sdk-rust,Apache-2.0,"AWS Rust SDK Team <<EMAIL>>, Russell Cohen <<EMAIL>>"
aws-sdk-sns,https://github.com/awslabs/aws-sdk-rust,Apache-2.0,"AWS Rust SDK Team <<EMAIL>>, Russell Cohen <<EMAIL>>"
aws-sdk-sqs,https://github.com/awslabs/aws-sdk-rust,Apache-2.0,"AWS Rust SDK Team <<EMAIL>>, Russell Cohen <<EMAIL>>"
aws-sdk-sts,https://github.com/awslabs/aws-sdk-rust,Apache-2.0,"AWS Rust SDK Team <<EMAIL>>, Russell Cohen <<EMAIL>>"
aws-sigv4,https://github.com/smithy-lang/smithy-rs,Apache-2.0,"AWS Rust SDK Team <<EMAIL>>, David Barsky <<EMAIL>>"
aws-smithy-async,https://github.com/smithy-lang/smithy-rs,Apache-2.0,"AWS Rust SDK Team <<EMAIL>>, John DiSanti <<EMAIL>>"
aws-smithy-checksums,https://github.com/smithy-lang/smithy-rs,Apache-2.0,"AWS Rust SDK Team <<EMAIL>>, Zelda Hessler <<EMAIL>>"
aws-smithy-eventstream,https://github.com/smithy-lang/smithy-rs,Apache-2.0,"AWS Rust SDK Team <<EMAIL>>, John DiSanti <<EMAIL>>"
aws-smithy-http,https://github.com/smithy-lang/smithy-rs,Apache-2.0,"AWS Rust SDK Team <<EMAIL>>, Russell Cohen <<EMAIL>>"
aws-smithy-json,https://github.com/smithy-lang/smithy-rs,Apache-2.0,"AWS Rust SDK Team <<EMAIL>>, John DiSanti <<EMAIL>>"
aws-smithy-query,https://github.com/smithy-lang/smithy-rs,Apache-2.0,"AWS Rust SDK Team <<EMAIL>>, John DiSanti <<EMAIL>>"
aws-smithy-runtime,https://github.com/smithy-lang/smithy-rs,Apache-2.0,"AWS Rust SDK Team <<EMAIL>>, Zelda Hessler <<EMAIL>>"
aws-smithy-runtime-api,https://github.com/smithy-lang/smithy-rs,Apache-2.0,"AWS Rust SDK Team <<EMAIL>>, Zelda Hessler <<EMAIL>>"
aws-smithy-types,https://github.com/smithy-lang/smithy-rs,Apache-2.0,"AWS Rust SDK Team <<EMAIL>>, Russell Cohen <<EMAIL>>"
aws-smithy-xml,https://github.com/smithy-lang/smithy-rs,Apache-2.0,"AWS Rust SDK Team <<EMAIL>>, Russell Cohen <<EMAIL>>"
aws-types,https://github.com/smithy-lang/smithy-rs,Apache-2.0,"AWS Rust SDK Team <<EMAIL>>, Russell Cohen <<EMAIL>>"
axum,https://github.com/tokio-rs/axum,MIT,The axum Authors
axum-core,https://github.com/tokio-rs/axum,MIT,The axum-core Authors
azure_core,https://github.com/azure/azure-sdk-for-rust,MIT,Microsoft Corp.
azure_identity,https://github.com/azure/azure-sdk-for-rust,MIT,Microsoft Corp.
azure_storage,https://github.com/azure/azure-sdk-for-rust,MIT,Microsoft Corp.
azure_storage_blobs,https://github.com/azure/azure-sdk-for-rust,MIT,Microsoft Corp.
backoff,https://github.com/ihrwein/backoff,MIT OR Apache-2.0,Tibor Benke <<EMAIL>>
backon,https://github.com/Xuanwo/backon,Apache-2.0,Xuanwo <<EMAIL>>
backtrace,https://github.com/rust-lang/backtrace-rs,MIT OR Apache-2.0,The Rust Project Developers
base16,https://github.com/thomcc/rust-base16,CC0-1.0,Thom Chiovoloni <<EMAIL>>
base16ct,https://github.com/RustCrypto/formats/tree/master/base16ct,Apache-2.0 OR MIT,RustCrypto Developers
base64,https://github.com/marshallpierce/rust-base64,MIT OR Apache-2.0,"Alice Maz <<EMAIL>>, Marshall Pierce <<EMAIL>>"
base64-simd,https://github.com/Nugine/simd,MIT,The base64-simd Authors
base64ct,https://github.com/RustCrypto/formats/tree/master/base64ct,Apache-2.0 OR MIT,RustCrypto Developers
bit-set,https://github.com/contain-rs/bit-set,MIT OR Apache-2.0,Alexis Beingessner <<EMAIL>>
bit-vec,https://github.com/contain-rs/bit-vec,MIT OR Apache-2.0,Alexis Beingessner <<EMAIL>>
bitflags,https://github.com/bitflags/bitflags,MIT OR Apache-2.0,The Rust Project Developers
bitmask-enum,https://github.com/Lukas3674/rust-bitmask-enum,MIT OR Apache-2.0,Lukas3674 <<EMAIL>>
bitvec,https://github.com/bitvecto-rs/bitvec,MIT,The bitvec Authors
block-buffer,https://github.com/RustCrypto/utils,MIT OR Apache-2.0,RustCrypto Developers
block-padding,https://github.com/RustCrypto/utils,MIT OR Apache-2.0,RustCrypto Developers
blocking,https://github.com/smol-rs/blocking,Apache-2.0 OR MIT,Stjepan Glavina <<EMAIL>>
bloomy,https://docs.rs/bloomy/,MIT,"Aleksandr Bezobchuk <<EMAIL>>, Alexis Sellier <<EMAIL>>"
bollard,https://github.com/fussybeaver/bollard,Apache-2.0,Bollard contributors
borsh,https://github.com/near/borsh-rs,MIT OR Apache-2.0,Near Inc <<EMAIL>>
borsh-derive,https://github.com/nearprotocol/borsh,Apache-2.0,Near Inc <<EMAIL>>
bson,https://github.com/mongodb/bson-rust,MIT,"Y. T. Chung <<EMAIL>>, Kevin Yeh <<EMAIL>>, Saghm Rossi <<EMAIL>>, Patrick Freed <<EMAIL>>, Isabel Atkinson <<EMAIL>>, Abraham Egnor <<EMAIL>>"
bstr,https://github.com/BurntSushi/bstr,MIT OR Apache-2.0,Andrew Gallant <<EMAIL>>
bumpalo,https://github.com/fitzgen/bumpalo,MIT OR Apache-2.0,Nick Fitzgerald <<EMAIL>>
bytecheck,https://github.com/djkoloski/bytecheck,MIT,David Koloski <<EMAIL>>
byteorder,https://github.com/BurntSushi/byteorder,Unlicense OR MIT,Andrew Gallant <<EMAIL>>
bytes,https://github.com/carllerche/bytes,MIT,Carl Lerche <<EMAIL>>
bytes,https://github.com/tokio-rs/bytes,MIT,"Carl Lerche <<EMAIL>>, Sean McArthur <<EMAIL>>"
bytes-utils,https://github.com/vorner/bytes-utils,Apache-2.0 OR MIT,Michal 'vorner' Vaner <<EMAIL>>
bytesize,https://github.com/hyunsik/bytesize,Apache-2.0,Hyunsik Choi <<EMAIL>>
cassowary,https://github.com/dylanede/cassowary-rs,MIT  OR  Apache-2.0,Dylan Ede <<EMAIL>>
castaway,https://github.com/sagebind/castaway,MIT,Stephen M. Coakley <<EMAIL>>
cbc,https://github.com/RustCrypto/block-modes,MIT OR Apache-2.0,RustCrypto Developers
cesu8,https://github.com/emk/cesu8-rs,Apache-2.0 OR MIT,Eric Kidd <*******************>
cfb-mode,https://github.com/RustCrypto/block-modes,MIT OR Apache-2.0,RustCrypto Developers
cfg-if,https://github.com/alexcrichton/cfg-if,MIT OR Apache-2.0,Alex Crichton <<EMAIL>>
chacha20,https://github.com/RustCrypto/stream-ciphers,Apache-2.0 OR MIT,RustCrypto Developers
chacha20poly1305,https://github.com/RustCrypto/AEADs/tree/master/chacha20poly1305,Apache-2.0 OR MIT,RustCrypto Developers
charset,https://github.com/hsivonen/charset,MIT OR Apache-2.0,Henri Sivonen <<EMAIL>>
chrono,https://github.com/chronotope/chrono,MIT OR Apache-2.0,The chrono Authors
chrono-tz,https://github.com/chronotope/chrono-tz,MIT OR Apache-2.0,The chrono-tz Authors
cidr,https://github.com/stbuehler/rust-cidr,MIT,Stefan Bühler <<EMAIL>>
cidr-utils,https://github.com/magiclen/cidr-utils,MIT,Magic Len <<EMAIL>>
cipher,https://github.com/RustCrypto/traits,MIT OR Apache-2.0,RustCrypto Developers
clap,https://github.com/clap-rs/clap,MIT,Kevin K. <<EMAIL>>
clap,https://github.com/clap-rs/clap,MIT OR Apache-2.0,The clap Authors
clap_builder,https://github.com/clap-rs/clap,MIT OR Apache-2.0,The clap_builder Authors
clap_derive,https://github.com/clap-rs/clap/tree/master/clap_derive,MIT OR Apache-2.0,The clap_derive Authors
clap_lex,https://github.com/clap-rs/clap/tree/master/clap_lex,MIT OR Apache-2.0,The clap_lex Authors
clipboard-win,https://github.com/DoumanAsh/clipboard-win,BSL-1.0,Douman <<EMAIL>>
codespan-reporting,https://github.com/brendanzab/codespan,Apache-2.0,Brendan Zabarauskas <<EMAIL>>
colorchoice,https://github.com/rust-cli/anstyle,MIT OR Apache-2.0,The colorchoice Authors
colored,https://github.com/mackwic/colored,MPL-2.0,Thomas Wickham <<EMAIL>>
combine,https://github.com/Marwes/combine,MIT,Markus Westerlind <<EMAIL>>
community-id,https://github.com/traceflight/rs-community-id,MIT OR Apache-2.0,Julian Wang <<EMAIL>>
compact_str,https://github.com/ParkMyCar/compact_str,MIT,Parker Timmerman <<EMAIL>>
concurrent-queue,https://github.com/smol-rs/concurrent-queue,Apache-2.0 OR MIT,"Stjepan Glavina <<EMAIL>>, Taiki Endo <<EMAIL>>, John Nunley <<EMAIL>>"
const-oid,https://github.com/RustCrypto/formats/tree/master/const-oid,Apache-2.0 OR MIT,RustCrypto Developers
const_fn,https://github.com/taiki-e/const_fn,Apache-2.0 OR MIT,The const_fn Authors
convert_case,https://github.com/rutrum/convert-case,MIT,David Purdum <<EMAIL>>
convert_case,https://github.com/rutrum/convert-case,MIT,Rutrum <<EMAIL>>
cookie-factory,https://github.com/rust-bakery/cookie-factory,MIT,"Geoffroy Couprie <<EMAIL>>, Pierre Chifflier <<EMAIL>>"
core-foundation,https://github.com/servo/core-foundation-rs,MIT  OR  Apache-2.0,The Servo Project Developers
core2,https://github.com/bbqsrc/core2,Apache-2.0 OR MIT,Brendan Molloy <<EMAIL>>
cpufeatures,https://github.com/RustCrypto/utils,MIT OR Apache-2.0,RustCrypto Developers
crc,https://github.com/mrhooray/crc-rs,MIT OR Apache-2.0,"Rui Hu <<EMAIL>>, Akhil Velagapudi <<EMAIL>>"
crc-catalog,https://github.com/akhilles/crc-catalog,MIT OR Apache-2.0,Akhil Velagapudi <<EMAIL>>
crc32c,https://github.com/zowens/crc32c,Apache-2.0 OR MIT,Zack Owens
crc32fast,https://github.com/srijs/rust-crc32fast,MIT OR Apache-2.0,"Sam Rijs <<EMAIL>>, Alex Crichton <<EMAIL>>"
crossbeam-epoch,https://github.com/crossbeam-rs/crossbeam,MIT OR Apache-2.0,The crossbeam-epoch Authors
crossbeam-queue,https://github.com/crossbeam-rs/crossbeam,MIT OR Apache-2.0,The crossbeam-queue Authors
crossbeam-utils,https://github.com/crossbeam-rs/crossbeam,MIT OR Apache-2.0,The crossbeam-utils Authors
crossterm,https://github.com/crossterm-rs/crossterm,MIT,T. Post
crossterm_winapi,https://github.com/crossterm-rs/crossterm-winapi,MIT,T. Post
crypto-bigint,https://github.com/RustCrypto/crypto-bigint,Apache-2.0 OR MIT,RustCrypto Developers
crypto-common,https://github.com/RustCrypto/traits,MIT OR Apache-2.0,RustCrypto Developers
crypto_secretbox,https://github.com/RustCrypto/nacl-compat/tree/master/crypto_secretbox,Apache-2.0 OR MIT,RustCrypto Developers
csv,https://github.com/BurntSushi/rust-csv,Unlicense OR MIT,Andrew Gallant <<EMAIL>>
ctr,https://github.com/RustCrypto/block-modes,MIT OR Apache-2.0,RustCrypto Developers
curve25519-dalek,https://github.com/dalek-cryptography/curve25519-dalek/tree/main/curve25519-dalek,BSD-3-Clause,"Isis Lovecruft <<EMAIL>>, Henry de Valence <<EMAIL>>"
curve25519-dalek-derive,https://github.com/dalek-cryptography/curve25519-dalek,MIT OR Apache-2.0,The curve25519-dalek-derive Authors
darling,https://github.com/TedDriggs/darling,MIT,Ted Driggs <<EMAIL>>
dary_heap,https://github.com/hanmertens/dary_heap,MIT OR Apache-2.0,Han Mertens <<EMAIL>>
dashmap,https://github.com/xacrimon/dashmap,MIT,Acrimon <<EMAIL>>
data-encoding,https://github.com/ia0/data-encoding,MIT,Julien Cretin <**********>
data-url,https://github.com/servo/rust-url,MIT OR Apache-2.0,Simon Sapin <<EMAIL>>
debug-helper,https://github.com/magiclen/debug-helper,MIT,Magic Len <<EMAIL>>
der,https://github.com/RustCrypto/formats/tree/master/der,Apache-2.0 OR MIT,RustCrypto Developers
deranged,https://github.com/jhpratt/deranged,MIT OR Apache-2.0,Jacob Pratt <<EMAIL>>
derivative,https://github.com/mcarton/rust-derivative,MIT OR Apache-2.0,mcarton <<EMAIL>>
derive_arbitrary,https://github.com/rust-fuzz/arbitrary,MIT OR Apache-2.0,"The Rust-Fuzz Project Developers, Nick Fitzgerald <<EMAIL>>, Manish Goregaokar <<EMAIL>>, Andre Bogus <<EMAIL>>, Corey Farwell <<EMAIL>>"
derive_more,https://github.com/JelteF/derive_more,MIT,Jelte Fennema <<EMAIL>>
digest,https://github.com/RustCrypto/traits,MIT OR Apache-2.0,RustCrypto Developers
dirs-next,https://github.com/xdg-rs/dirs,MIT OR Apache-2.0,The @xdg-rs members
dirs-sys-next,https://github.com/xdg-rs/dirs/tree/master/dirs-sys,MIT OR Apache-2.0,The @xdg-rs members
dns-lookup,https://github.com/keeperofdakeys/dns-lookup,MIT OR Apache-2.0,Josh Driver <<EMAIL>>
doc-comment,https://github.com/GuillaumeGomez/doc-comment,MIT,Guillaume Gomez <<EMAIL>>
dyn-clone,https://github.com/dtolnay/dyn-clone,MIT OR Apache-2.0,David Tolnay <<EMAIL>>
ecdsa,https://github.com/RustCrypto/signatures/tree/master/ecdsa,Apache-2.0 OR MIT,RustCrypto Developers
ed25519,https://github.com/RustCrypto/signatures/tree/master/ed25519,Apache-2.0 OR MIT,RustCrypto Developers
ed25519-dalek,https://github.com/dalek-cryptography/ed25519-dalek,BSD-3-Clause,"isis lovecruft <<EMAIL>>, Tony Arcieri <<EMAIL>>, Michael Rosenberg <<EMAIL>>"
either,https://github.com/bluss/either,MIT OR Apache-2.0,bluss
elliptic-curve,https://github.com/RustCrypto/traits/tree/master/elliptic-curve,Apache-2.0 OR MIT,RustCrypto Developers
encode_unicode,https://github.com/tormol/encode_unicode,Apache-2.0 OR MIT,Torbjørn Birch Moltu <<EMAIL>>
encoding_rs,https://github.com/hsivonen/encoding_rs,(Apache-2.0 OR MIT) AND BSD-3-Clause,Henri Sivonen <<EMAIL>>
endian-type,https://github.com/Lolirofle/endian-type,MIT,Lolirofle <<EMAIL>>
enum-as-inner,https://github.com/bluejekyll/enum-as-inner,MIT OR Apache-2.0,Benjamin Fry <<EMAIL>>
enum_dispatch,https://gitlab.com/antonok/enum_dispatch,MIT OR Apache-2.0,Anton Lazarev <https://antonok.com>
enumflags2,https://github.com/meithecatte/enumflags2,MIT OR Apache-2.0,"maik klein <<EMAIL>>, Maja Kądziołka <<EMAIL>>"
env_logger,https://github.com/env-logger-rs/env_logger,MIT OR Apache-2.0,The Rust Project Developers
equivalent,https://github.com/cuviper/equivalent,Apache-2.0 OR MIT,The equivalent Authors
erased-serde,https://github.com/dtolnay/erased-serde,MIT OR Apache-2.0,David Tolnay <<EMAIL>>
errno,https://github.com/lambda-fairy/rust-errno,MIT OR Apache-2.0,Chris Wong <<EMAIL>>
error-chain,https://github.com/rust-lang-nursery/error-chain,MIT OR Apache-2.0,"Brian Anderson <<EMAIL>>, Paul Colomiets <<EMAIL>>, Colin Kiegel <<EMAIL>>, Yamakaky <<EMAIL>>, Andrew Gauger <<EMAIL>>"
error-code,https://github.com/DoumanAsh/error-code,BSL-1.0,Douman <<EMAIL>>
event-listener,https://github.com/smol-rs/event-listener,Apache-2.0 OR MIT,Stjepan Glavina <<EMAIL>>
event-listener-strategy,https://github.com/smol-rs/event-listener,Apache-2.0 OR MIT,John Nunley <<EMAIL>>
executor-trait,https://github.com/amqp-rs/executor-trait,Apache-2.0 OR MIT,Marc-Antoine Perennou <<EMAIL>>
exitcode,https://github.com/benwilber/exitcode,Apache-2.0,Ben Wilber <<EMAIL>>
fakedata_generator,https://github.com/kevingimbel/fakedata_generator,MIT,Kevin Gimbel <<EMAIL>>
fallible-iterator,https://github.com/sfackler/rust-fallible-iterator,MIT OR Apache-2.0,Steven Fackler <<EMAIL>>
fastrand,https://github.com/smol-rs/fastrand,Apache-2.0 OR MIT,Stjepan Glavina <<EMAIL>>
ff,https://github.com/zkcrypto/ff,MIT OR Apache-2.0,"Sean Bowe <<EMAIL>>, Jack Grigg <<EMAIL>>"
fiat-crypto,https://github.com/mit-plv/fiat-crypto,MIT OR Apache-2.0 OR BSD-1-Clause,Fiat Crypto library authors <<EMAIL>>
filetime,https://github.com/alexcrichton/filetime,MIT OR Apache-2.0,Alex Crichton <<EMAIL>>
finl_unicode,https://github.com/dahosek/finl_unicode,MIT OR Apache-2.0,The finl_unicode Authors
flagset,https://github.com/enarx/flagset,Apache-2.0,Nathaniel McCallum <<EMAIL>>
flate2,https://github.com/rust-lang/flate2-rs,MIT OR Apache-2.0,"Alex Crichton <<EMAIL>>, Josh Triplett <<EMAIL>>"
float_eq,https://github.com/jtempest/float_eq-rs,MIT OR Apache-2.0,jtempest
flume,https://github.com/zesterer/flume,Apache-2.0 OR MIT,Joshua Barretto <<EMAIL>>
fnv,https://github.com/servo/rust-fnv,Apache-2.0  OR  MIT,Alex Crichton <<EMAIL>>
foreign-types,https://github.com/sfackler/foreign-types,MIT OR Apache-2.0,Steven Fackler <<EMAIL>>
fsevent-sys,https://github.com/octplane/fsevent-rust/tree/master/fsevent-sys,MIT,Pierre Baillet <<EMAIL>>
fslock,https://github.com/brunoczim/fslock,MIT,The fslock Authors
funty,https://github.com/myrrlyn/funty,MIT,myrrlyn <<EMAIL>>
futures,https://github.com/rust-lang-nursery/futures-rs,MIT OR Apache-2.0,Alex Crichton <<EMAIL>>
futures,https://github.com/rust-lang/futures-rs,MIT OR Apache-2.0,The futures Authors
futures-channel,https://github.com/rust-lang/futures-rs,MIT OR Apache-2.0,The futures-channel Authors
futures-core,https://github.com/rust-lang/futures-rs,MIT OR Apache-2.0,The futures-core Authors
futures-executor,https://github.com/rust-lang/futures-rs,MIT OR Apache-2.0,The futures-executor Authors
futures-io,https://github.com/rust-lang/futures-rs,MIT OR Apache-2.0,The futures-io Authors
futures-lite,https://github.com/smol-rs/futures-lite,Apache-2.0 OR MIT,"Stjepan Glavina <<EMAIL>>, Contributors to futures-rs"
futures-macro,https://github.com/rust-lang/futures-rs,MIT OR Apache-2.0,The futures-macro Authors
futures-sink,https://github.com/rust-lang/futures-rs,MIT OR Apache-2.0,The futures-sink Authors
futures-task,https://github.com/rust-lang/futures-rs,MIT OR Apache-2.0,The futures-task Authors
futures-timer,https://github.com/async-rs/futures-timer,MIT OR Apache-2.0,Alex Crichton <<EMAIL>>
futures-util,https://github.com/rust-lang/futures-rs,MIT OR Apache-2.0,The futures-util Authors
generic-array,https://github.com/fizyk20/generic-array,MIT,"Bartłomiej Kamiński <<EMAIL>>, Aaron Trent <<EMAIL>>"
getrandom,https://github.com/rust-random/getrandom,MIT OR Apache-2.0,The Rand Project Developers
gimli,https://github.com/gimli-rs/gimli,MIT OR Apache-2.0,The gimli Authors
glob,https://github.com/rust-lang/glob,MIT OR Apache-2.0,The Rust Project Developers
goauth,https://github.com/durch/rust-goauth,MIT,Drazen Urch <<EMAIL>>
governor,https://github.com/antifuchs/governor,MIT,Andreas Fuchs <<EMAIL>>
graphql-introspection-query,https://github.com/graphql-rust/graphql-client,Apache-2.0 OR MIT,Tom Houlé <<EMAIL>>
graphql-parser,https://github.com/graphql-rust/graphql-parser,MIT OR Apache-2.0,Paul Colomiets <<EMAIL>>
graphql_client,https://github.com/graphql-rust/graphql-client,Apache-2.0 OR MIT,Tom Houlé <<EMAIL>>
graphql_client_codegen,https://github.com/graphql-rust/graphql-client,Apache-2.0 OR MIT,Tom Houlé <<EMAIL>>
graphql_query_derive,https://github.com/graphql-rust/graphql-client,Apache-2.0 OR MIT,Tom Houlé <<EMAIL>>
greptime-proto,https://github.com/GreptimeTeam/greptime-proto,Apache-2.0,The greptime-proto Authors
greptimedb-client,https://github.com/GreptimeTeam/greptimedb-ingester-rust,Apache-2.0,The greptimedb-client Authors
grok,https://github.com/daschl/grok,Apache-2.0,Michael Nitschinger <<EMAIL>>
group,https://github.com/zkcrypto/group,MIT OR Apache-2.0,"Sean Bowe <<EMAIL>>, Jack Grigg <<EMAIL>>"
h2,https://github.com/hyperium/h2,MIT,"Carl Lerche <<EMAIL>>, Sean McArthur <<EMAIL>>"
hash_hasher,https://github.com/Fraser999/Hash-Hasher,Apache-2.0 OR MIT,Fraser Hutchison <<EMAIL>>
hashbrown,https://github.com/rust-lang/hashbrown,MIT OR Apache-2.0,Amanieu d'Antras <<EMAIL>>
headers,https://github.com/hyperium/headers,MIT,Sean McArthur <<EMAIL>>
heck,https://github.com/withoutboats/heck,MIT OR Apache-2.0,The heck Authors
heck,https://github.com/withoutboats/heck,MIT OR Apache-2.0,Without Boats <<EMAIL>>
heim,https://github.com/heim-rs/heim,Apache-2.0 OR MIT,svartalf <<EMAIL>>
hermit-abi,https://github.com/hermitcore/hermit-rs,MIT OR Apache-2.0,Stefan Lankes
hermit-abi,https://github.com/hermitcore/libhermit-rs,MIT OR Apache-2.0,Stefan Lankes
hex,https://github.com/KokaKiwi/rust-hex,MIT OR Apache-2.0,KokaKiwi <<EMAIL>>
hickory-proto,https://github.com/hickory-dns/hickory-dns,MIT OR Apache-2.0,The contributors to Hickory DNS
hkdf,https://github.com/RustCrypto/KDFs,MIT OR Apache-2.0,RustCrypto Developers
hmac,https://github.com/RustCrypto/MACs,MIT OR Apache-2.0,RustCrypto Developers
home,https://github.com/rust-lang/cargo,MIT OR Apache-2.0,Brian Anderson <<EMAIL>>
hostname,https://github.com/svartalf/hostname,MIT,"fengcen <<EMAIL>>, svartalf <<EMAIL>>"
http,https://github.com/hyperium/http,MIT OR Apache-2.0,"Alex Crichton <<EMAIL>>, Carl Lerche <<EMAIL>>, Sean McArthur <<EMAIL>>"
http-body,https://github.com/hyperium/http-body,MIT,"Carl Lerche <<EMAIL>>, Lucio Franco <<EMAIL>>, Sean McArthur <<EMAIL>>"
http-range-header,https://github.com/MarcusGrass/parse-range-headers,MIT,The http-range-header Authors
http-serde,https://gitlab.com/kornelski/http-serde,Apache-2.0 OR MIT,Kornel <<EMAIL>>
http-types,https://github.com/http-rs/http-types,MIT OR Apache-2.0,Yoshua Wuyts <<EMAIL>>
httparse,https://github.com/seanmonstar/httparse,MIT OR Apache-2.0,Sean McArthur <<EMAIL>>
httpdate,https://github.com/pyfisch/httpdate,MIT OR Apache-2.0,Pyfisch <<EMAIL>>
hyper,https://github.com/hyperium/hyper,MIT,Sean McArthur <<EMAIL>>
hyper-named-pipe,https://github.com/fussybeaver/hyper-named-pipe,Apache-2.0,The hyper-named-pipe Authors
hyper-openssl,https://github.com/sfackler/hyper-openssl,MIT OR Apache-2.0,Steven Fackler <<EMAIL>>
hyper-proxy,https://github.com/tafia/hyper-proxy,MIT,Johann Tuffe <<EMAIL>>
hyper-rustls,https://github.com/rustls/hyper-rustls,Apache-2.0 OR ISC OR MIT,The hyper-rustls Authors
hyper-timeout,https://github.com/hjr3/hyper-timeout,MIT OR Apache-2.0,Herman J. Radtke III <<EMAIL>>
hyper-tls,https://github.com/hyperium/hyper-tls,MIT OR Apache-2.0,Sean McArthur <<EMAIL>>
hyper-util,https://github.com/hyperium/hyper-util,MIT,Sean McArthur <<EMAIL>>
hyperlocal-next,https://github.com/softprops/hyperlocal,MIT,softprops <<EMAIL>>
iana-time-zone,https://github.com/strawlab/iana-time-zone,MIT OR Apache-2.0,"Andrew Straw <<EMAIL>>, René Kijewski <<EMAIL>>, Ryan Lopopolo <<EMAIL>>"
iana-time-zone-haiku,https://github.com/strawlab/iana-time-zone,MIT OR Apache-2.0,René Kijewski <<EMAIL>>
ident_case,https://github.com/TedDriggs/ident_case,MIT OR Apache-2.0,Ted Driggs <<EMAIL>>
indexmap,https://github.com/bluss/indexmap,Apache-2.0 OR MIT,The indexmap Authors
indexmap,https://github.com/indexmap-rs/indexmap,Apache-2.0 OR MIT,The indexmap Authors
indoc,https://github.com/dtolnay/indoc,MIT OR Apache-2.0,David Tolnay <<EMAIL>>
infer,https://github.com/bojand/infer,MIT,Bojan <<EMAIL>>
inotify,https://github.com/hannobraun/inotify,ISC,"Hanno Braun <<EMAIL>>, Félix Saparelli <<EMAIL>>, Cristian Kubis <<EMAIL>>, Frank Denis <<EMAIL>>"
inotify-sys,https://github.com/hannobraun/inotify-sys,ISC,Hanno Braun <<EMAIL>>
inout,https://github.com/RustCrypto/utils,MIT OR Apache-2.0,RustCrypto Developers
instant,https://github.com/sebcrozet/instant,BSD-3-Clause,sebcrozet <<EMAIL>>
inventory,https://github.com/dtolnay/inventory,MIT OR Apache-2.0,David Tolnay <<EMAIL>>
io-lifetimes,https://github.com/sunfishcode/io-lifetimes,Apache-2.0 WITH LLVM-exception OR Apache-2.0 OR MIT,Dan Gohman <<EMAIL>>
iovec,https://github.com/carllerche/iovec,MIT OR Apache-2.0,Carl Lerche <<EMAIL>>
ipconfig,https://github.com/liranringel/ipconfig,MIT OR Apache-2.0,Liran Ringel <<EMAIL>>
ipnet,https://github.com/krisprice/ipnet,MIT OR Apache-2.0,Kris Price <<EMAIL>>
ipnetwork,https://github.com/achanda/ipnetwork,MIT OR Apache-2.0,"Abhishek Chanda <<EMAIL>>, Linus Färnstrand <<EMAIL>>"
is-terminal,https://github.com/sunfishcode/is-terminal,MIT,"softprops <<EMAIL>>, Dan Gohman <<EMAIL>>"
itertools,https://github.com/rust-itertools/itertools,MIT OR Apache-2.0,bluss
itoa,https://github.com/dtolnay/itoa,MIT OR Apache-2.0,David Tolnay <<EMAIL>>
jni,https://github.com/jni-rs/jni-rs,MIT OR Apache-2.0,Josh Chase <<EMAIL>>
jni-sys,https://github.com/sfackler/rust-jni-sys,MIT OR Apache-2.0,Steven Fackler <<EMAIL>>
js-sys,https://github.com/rustwasm/wasm-bindgen/tree/master/crates/js-sys,MIT OR Apache-2.0,The wasm-bindgen Developers
json-patch,https://github.com/idubrov/json-patch,MIT OR Apache-2.0,Ivan Dubrov <<EMAIL>>
jsonpath_lib,https://github.com/freestrings/jsonpath,MIT,Changseok Han <<EMAIL>>
k8s-openapi,https://github.com/Arnavion/k8s-openapi,Apache-2.0,Arnavion <<EMAIL>>
keccak,https://github.com/RustCrypto/sponges/tree/master/keccak,Apache-2.0 OR MIT,RustCrypto Developers
kqueue,https://gitlab.com/rust-kqueue/rust-kqueue,MIT,William Orr <<EMAIL>>
kqueue-sys,https://gitlab.com/rust-kqueue/rust-kqueue-sys,MIT,"William Orr <<EMAIL>>, Daniel (dmilith) Dettlaff <<EMAIL>>"
krb5-src,https://github.com/MaterializeInc/rust-krb5-src,Apache-2.0,"Materialize, Inc."
kube,https://github.com/kube-rs/kube,Apache-2.0,"clux <<EMAIL>>, Natalie Klestrup Röijezon <<EMAIL>>, kazk <<EMAIL>>"
kube-core,https://github.com/kube-rs/kube,Apache-2.0,"clux <<EMAIL>>, kazk <<EMAIL>>"
kube-runtime,https://github.com/kube-rs/kube,Apache-2.0,"Natalie Klestrup Röijezon <<EMAIL>>, clux <<EMAIL>>"
lalrpop-util,https://github.com/lalrpop/lalrpop,Apache-2.0 OR MIT,Niko Matsakis <<EMAIL>>
lapin,https://github.com/amqp-rs/lapin,MIT,"Geoffroy Couprie <<EMAIL>>, Marc-Antoine Perennou <<EMAIL>>"
lazy_static,https://github.com/rust-lang-nursery/lazy-static.rs,MIT OR Apache-2.0,Marvin Löbel <<EMAIL>>
libc,https://github.com/rust-lang/libc,MIT OR Apache-2.0,The Rust Project Developers
libflate,https://github.com/sile/libflate,MIT,Takeru Ohta <<EMAIL>>
libm,https://github.com/rust-lang/libm,MIT OR Apache-2.0,Jorge Aparicio <<EMAIL>>
libz-sys,https://github.com/rust-lang/libz-sys,MIT OR Apache-2.0,"Alex Crichton <<EMAIL>>, Josh Triplett <<EMAIL>>, Sebastian Thiel <<EMAIL>>"
linked-hash-map,https://github.com/contain-rs/linked-hash-map,MIT OR Apache-2.0,"Stepan Koltsov <<EMAIL>>, Andrew Paseltiner <<EMAIL>>"
linked_hash_set,https://github.com/alexheretic/linked-hash-set,Apache-2.0,Alex Butler <<EMAIL>>
linux-raw-sys,https://github.com/sunfishcode/linux-raw-sys,Apache-2.0 WITH LLVM-exception OR Apache-2.0 OR MIT,Dan Gohman <<EMAIL>>
listenfd,https://github.com/mitsuhiko/rust-listenfd,Apache-2.0,Armin Ronacher <<EMAIL>>
lockfree-object-pool,https://github.com/EVaillant/lockfree-object-pool,BSL-1.0,Etienne Vaillant <<EMAIL>>
log,https://github.com/rust-lang/log,MIT OR Apache-2.0,The Rust Project Developers
lru,https://github.com/jeromefroe/lru-rs,MIT,Jerome Froelich <<EMAIL>>
lru-cache,https://github.com/contain-rs/lru-cache,MIT OR Apache-2.0,Stepan Koltsov <<EMAIL>>
lz4,https://github.com/10xGenomics/lz4-rs,MIT,"Jens Heyens <<EMAIL>>, Artem V. Navrotskiy <<EMAIL>>, Patrick Marks <<EMAIL>>"
macaddr,https://github.com/svartalf/rust-macaddr,Apache-2.0 OR MIT,svartalf <<EMAIL>>
mach,https://github.com/fitzgen/mach,BSD-2-Clause,"Nick Fitzgerald <<EMAIL>>, David Cuddeback <<EMAIL>>, Gonzalo Brito Gadeschi <<EMAIL>>"
mach2,https://github.com/JohnTitor/mach2,BSD-2-Clause OR MIT OR Apache-2.0,The mach2 Authors
malloc_buf,https://github.com/SSheldon/malloc_buf,MIT,Steven Sheldon
match_cfg,https://github.com/gnzlbg/match_cfg,MIT OR Apache-2.0,gnzlbg <<EMAIL>>
matchers,https://github.com/hawkw/matchers,MIT,Eliza Weisman <<EMAIL>>
matches,https://github.com/SimonSapin/rust-std-candidates,MIT,The matches Authors
matchit,https://github.com/ibraheemdev/matchit,MIT AND BSD-3-Clause,Ibraheem Ahmed <<EMAIL>>
maxminddb,https://github.com/oschwald/maxminddb-rust,ISC,Gregory J. Oschwald <<EMAIL>>
md-5,https://github.com/RustCrypto/hashes,MIT OR Apache-2.0,RustCrypto Developers
memchr,https://github.com/BurntSushi/memchr,Unlicense OR MIT,"Andrew Gallant <<EMAIL>>, bluss"
memmap2,https://github.com/RazrFalcon/memmap2-rs,MIT OR Apache-2.0,"Dan Burkert <<EMAIL>>, Yevhenii Reizner <<EMAIL>>"
memoffset,https://github.com/Gilnaa/memoffset,MIT,Gilad Naaman <<EMAIL>>
metrics,https://github.com/metrics-rs/metrics,MIT,Toby Lawrence <<EMAIL>>
metrics-tracing-context,https://github.com/metrics-rs/metrics,MIT,MOZGIII <<EMAIL>>
mime,https://github.com/hyperium/mime,MIT OR Apache-2.0,Sean McArthur <<EMAIL>>
mime_guess,https://github.com/abonander/mime_guess,MIT,Austin Bonander <<EMAIL>>
minimal-lexical,https://github.com/Alexhuszagh/minimal-lexical,MIT OR Apache-2.0,Alex Huszagh <<EMAIL>>
miniz_oxide,https://github.com/Frommi/miniz_oxide/tree/master/miniz_oxide,MIT OR Zlib OR Apache-2.0,"Frommi <<EMAIL>>, oyvindln <<EMAIL>>"
mio,https://github.com/tokio-rs/mio,MIT,"Carl Lerche <<EMAIL>>, Thomas de Zeeuw <<EMAIL>>, Tokio Contributors <<EMAIL>>"
mlua,https://github.com/khvzak/mlua,MIT,"Aleksandr Orlenko <<EMAIL>>, kyren <<EMAIL>>"
mlua-sys,https://github.com/khvzak/mlua,MIT,Aleksandr Orlenko <<EMAIL>>
mlua_derive,https://github.com/khvzak/mlua,MIT,Aleksandr Orlenko <<EMAIL>>
mongodb,https://github.com/mongodb/mongo-rust-driver,Apache-2.0,"Saghm Rossi <<EMAIL>>, Patrick Freed <<EMAIL>>, Isabel Atkinson <<EMAIL>>, Abraham Egnor <<EMAIL>>, Kaitlin Mahar <<EMAIL>>"
multer,https://github.com/rousan/multer-rs,MIT,Rousan Ali <<EMAIL>>
native-tls,https://github.com/sfackler/rust-native-tls,MIT OR Apache-2.0,Steven Fackler <<EMAIL>>
ndk-context,https://github.com/rust-windowing/android-ndk-rs,MIT OR Apache-2.0,The Rust Windowing contributors
nibble_vec,https://github.com/michaelsproul/rust_nibble_vec,MIT,Michael Sproul <<EMAIL>>
nix,https://github.com/nix-rust/nix,MIT,The nix-rust Project Developers
nkeys,https://github.com/wasmcloud/nkeys,Apache-2.0,wasmCloud Team
no-proxy,https://github.com/jdrouet/no-proxy,MIT,Jérémie Drouet <<EMAIL>>
no-std-compat,https://gitlab.com/jD91mZM2/no-std-compat,MIT,jD91mZM2 <<EMAIL>>
nom,https://github.com/Geal/nom,MIT,<EMAIL>
nonzero_ext,https://github.com/antifuchs/nonzero_ext,Apache-2.0,Andreas Fuchs <<EMAIL>>
notify,https://github.com/notify-rs/notify,CC0-1.0,"Félix Saparelli <<EMAIL>>, Daniel Faust <<EMAIL>>, Aron Heinecke <<EMAIL>>"
ntapi,https://github.com/MSxDOS/ntapi,Apache-2.0 OR MIT,MSxDOS <<EMAIL>>
nu-ansi-term,https://github.com/nushell/nu-ansi-term,MIT,"<EMAIL>, Ryan Scheel (Havvy) <<EMAIL>>, Josh Triplett <<EMAIL>>, The Nushell Project Developers"
nuid,https://github.com/casualjim/rs-nuid,Apache-2.0,Ivan Porto Carrero <<EMAIL>>
num-bigint,https://github.com/rust-num/num-bigint,MIT OR Apache-2.0,The Rust Project Developers
num-bigint-dig,https://github.com/dignifiedquire/num-bigint,MIT OR Apache-2.0,"dignifiedquire <<EMAIL>>, The Rust Project Developers"
num-format,https://github.com/bcmyers/num-format,MIT OR Apache-2.0,Brian Myers <<EMAIL>>
num-integer,https://github.com/rust-num/num-integer,MIT OR Apache-2.0,The Rust Project Developers
num-iter,https://github.com/rust-num/num-iter,MIT OR Apache-2.0,The Rust Project Developers
num-rational,https://github.com/rust-num/num-rational,MIT OR Apache-2.0,The Rust Project Developers
num-traits,https://github.com/rust-num/num-traits,MIT OR Apache-2.0,The Rust Project Developers
num_cpus,https://github.com/seanmonstar/num_cpus,MIT OR Apache-2.0,Sean McArthur <<EMAIL>>
num_enum,https://github.com/illicitonion/num_enum,BSD-3-Clause OR MIT OR Apache-2.0,"Daniel Wagner-Hall <<EMAIL>>, Daniel Henry-Mantilla <<EMAIL>>, Vincent Esche <<EMAIL>>"
num_threads,https://github.com/jhpratt/num_threads,MIT OR Apache-2.0,Jacob Pratt <<EMAIL>>
number_prefix,https://github.com/ogham/rust-number-prefix,MIT,Benjamin Sago <<EMAIL>>
oauth2,https://github.com/ramosbugs/oauth2-rs,MIT OR Apache-2.0,"Alex Crichton <<EMAIL>>, Florin Lipan <<EMAIL>>, David A. Ramos <<EMAIL>>"
objc,http://github.com/SSheldon/rust-objc,MIT,Steven Sheldon
object,https://github.com/gimli-rs/object,Apache-2.0 OR MIT,The object Authors
ofb,https://github.com/RustCrypto/block-modes,MIT OR Apache-2.0,RustCrypto Developers
once_cell,https://github.com/matklad/once_cell,MIT OR Apache-2.0,Aleksey Kladov <<EMAIL>>
onig,http://github.com/iwillspeak/rust-onig,MIT,"Will Speak <<EMAIL>>, Ivan Ivashchenko <<EMAIL>>"
opaque-debug,https://github.com/RustCrypto/utils,MIT OR Apache-2.0,RustCrypto Developers
opendal,https://github.com/apache/opendal,Apache-2.0,Apache OpenDAL <<EMAIL>>
openidconnect,https://github.com/ramosbugs/openidconnect-rs,MIT,David A. Ramos <<EMAIL>>
openssl,https://github.com/sfackler/rust-openssl,Apache-2.0,Steven Fackler <<EMAIL>>
openssl-macros,https://github.com/sfackler/rust-openssl,MIT OR Apache-2.0,The openssl-macros Authors
openssl-probe,https://github.com/alexcrichton/openssl-probe,MIT OR Apache-2.0,Alex Crichton <<EMAIL>>
openssl-sys,https://github.com/sfackler/rust-openssl,MIT,"Alex Crichton <<EMAIL>>, Steven Fackler <<EMAIL>>"
ordered-float,https://github.com/reem/rust-ordered-float,MIT,"Jonathan Reem <<EMAIL>>, Matt Brubeck <<EMAIL>>"
outref,https://github.com/Nugine/outref,MIT,The outref Authors
overload,https://github.com/danaugrs/overload,MIT,Daniel Salvadori <<EMAIL>>
p256,https://github.com/RustCrypto/elliptic-curves/tree/master/p256,Apache-2.0 OR MIT,RustCrypto Developers
p384,https://github.com/RustCrypto/elliptic-curves/tree/master/p384,Apache-2.0 OR MIT,"RustCrypto Developers, Frank Denis <<EMAIL>>"
pad,https://github.com/ogham/rust-pad,MIT,Ben S <<EMAIL>>
parking,https://github.com/smol-rs/parking,Apache-2.0 OR MIT,"Stjepan Glavina <<EMAIL>>, The Rust Project Developers"
parking_lot,https://github.com/Amanieu/parking_lot,MIT OR Apache-2.0,Amanieu d'Antras <<EMAIL>>
passt,https://github.com/kevingimbel/passt,MIT OR Apache-2.0,Kevin Gimbel <<EMAIL>>
paste,https://github.com/dtolnay/paste,MIT OR Apache-2.0,David Tolnay <<EMAIL>>
pbkdf2,https://github.com/RustCrypto/password-hashes/tree/master/pbkdf2,MIT OR Apache-2.0,RustCrypto Developers
peeking_take_while,https://github.com/fitzgen/peeking_take_while,MIT OR Apache-2.0,Nick Fitzgerald <<EMAIL>>
pem,https://github.com/jcreekmore/pem-rs,MIT,Jonathan Creekmore <<EMAIL>>
pem-rfc7468,https://github.com/RustCrypto/formats/tree/master/pem-rfc7468,Apache-2.0 OR MIT,RustCrypto Developers
pest,https://github.com/pest-parser/pest,MIT OR Apache-2.0,Dragoș Tiselice <<EMAIL>>
phf,https://github.com/rust-phf/rust-phf,MIT,Steven Fackler <<EMAIL>>
pin-project,https://github.com/taiki-e/pin-project,Apache-2.0 OR MIT,The pin-project Authors
pin-project-internal,https://github.com/taiki-e/pin-project,Apache-2.0 OR MIT,The pin-project-internal Authors
pin-project-lite,https://github.com/taiki-e/pin-project-lite,Apache-2.0 OR MIT,The pin-project-lite Authors
pin-utils,https://github.com/rust-lang-nursery/pin-utils,MIT OR Apache-2.0,Josef Brandl <<EMAIL>>
pinky-swear,https://github.com/amqp-rs/pinky-swear,BSD-2-Clause,Marc-Antoine Perennou <<EMAIL>>
piper,https://github.com/notgull/piper,MIT OR Apache-2.0,"Stjepan Glavina <<EMAIL>>, John Nunley <<EMAIL>>"
pkcs1,https://github.com/RustCrypto/formats/tree/master/pkcs1,Apache-2.0 OR MIT,RustCrypto Developers
pkcs8,https://github.com/RustCrypto/formats/tree/master/pkcs8,Apache-2.0 OR MIT,RustCrypto Developers
platforms,https://github.com/RustSec/platforms-crate,Apache-2.0 OR MIT,Tony Arcieri <<EMAIL>>
polling,https://github.com/smol-rs/polling,Apache-2.0 OR MIT,Stjepan Glavina <<EMAIL>>
polling,https://github.com/smol-rs/polling,Apache-2.0 OR MIT,"Stjepan Glavina <<EMAIL>>, John Nunley <<EMAIL>>"
poly1305,https://github.com/RustCrypto/universal-hashes,Apache-2.0 OR MIT,RustCrypto Developers
portable-atomic,https://github.com/taiki-e/portable-atomic,Apache-2.0 OR MIT,The portable-atomic Authors
postgres-openssl,https://github.com/sfackler/rust-postgres,MIT OR Apache-2.0,Steven Fackler <<EMAIL>>
postgres-protocol,https://github.com/sfackler/rust-postgres,MIT OR Apache-2.0,Steven Fackler <<EMAIL>>
postgres-types,https://github.com/sfackler/rust-postgres,MIT OR Apache-2.0,Steven Fackler <<EMAIL>>
powerfmt,https://github.com/jhpratt/powerfmt,MIT OR Apache-2.0,Jacob Pratt <<EMAIL>>
ppv-lite86,https://github.com/cryptocorrosion/cryptocorrosion,MIT OR Apache-2.0,The CryptoCorrosion Contributors
prettydiff,https://github.com/romankoblov/prettydiff,MIT,Roman Koblov <<EMAIL>>
prettytable-rs,https://github.com/phsym/prettytable-rs,BSD-3-Clause,Pierre-Henri Symoneaux
primeorder,https://github.com/RustCrypto/elliptic-curves/tree/master/primeorder,Apache-2.0 OR MIT,RustCrypto Developers
proc-macro-crate,https://github.com/bkchr/proc-macro-crate,MIT OR Apache-2.0,Bastian Köcher <***********>
proc-macro-error,https://gitlab.com/CreepySkeleton/proc-macro-error,MIT OR Apache-2.0,CreepySkeleton <<EMAIL>>
proc-macro-hack,https://github.com/dtolnay/proc-macro-hack,MIT OR Apache-2.0,David Tolnay <<EMAIL>>
proc-macro2,https://github.com/dtolnay/proc-macro2,MIT OR Apache-2.0,"David Tolnay <<EMAIL>>, Alex Crichton <<EMAIL>>"
proptest,https://github.com/proptest-rs/proptest,MIT OR Apache-2.0,Jason Lingle
prost,https://github.com/tokio-rs/prost,Apache-2.0,"Dan Burkert <<EMAIL>>, Lucio Franco <<EMAIL>, Tokio Contributors <<EMAIL>>"
prost-derive,https://github.com/tokio-rs/prost,Apache-2.0,"Dan Burkert <<EMAIL>>, Lucio Franco <<EMAIL>>, Tokio Contributors <<EMAIL>>"
prost-reflect,https://github.com/andrewhickman/prost-reflect,MIT OR Apache-2.0,Andrew Hickman <<EMAIL>>
psl,https://github.com/addr-rs/psl,MIT OR Apache-2.0,rushmorem <<EMAIL>>
psl-types,https://github.com/addr-rs/psl-types,MIT OR Apache-2.0,rushmorem <<EMAIL>>
ptr_meta,https://github.com/djkoloski/ptr_meta,MIT,David Koloski <<EMAIL>>
pulsar,https://github.com/streamnative/pulsar-rs,MIT OR Apache-2.0,"Colin Stearns <<EMAIL>>, Kevin Stenerson <<EMAIL>>, Geoffroy Couprie <<EMAIL>>"
quad-rand,https://github.com/not-fl3/quad-rand,MIT,not-fl3 <<EMAIL>>
quanta,https://github.com/metrics-rs/quanta,MIT,Toby Lawrence <<EMAIL>>
quick-error,http://github.com/tailhook/quick-error,MIT OR Apache-2.0,"Paul Colomiets <<EMAIL>>, Colin Kiegel <<EMAIL>>"
quick-xml,https://github.com/tafia/quick-xml,MIT,The quick-xml Authors
quickcheck,https://github.com/BurntSushi/quickcheck,Unlicense OR MIT,Andrew Gallant <<EMAIL>>
quote,https://github.com/dtolnay/quote,MIT OR Apache-2.0,David Tolnay <<EMAIL>>
quoted_printable,https://github.com/staktrace/quoted-printable,0BSD,Kartikaya Gupta <<EMAIL>>
radium,https://github.com/bitvecto-rs/radium,MIT,"Nika Layzell <<EMAIL>>, myrrlyn <<EMAIL>>"
radix_trie,https://github.com/michaelsproul/rust_radix_trie,MIT,Michael Sproul <<EMAIL>>
rand,https://github.com/rust-random/rand,MIT OR Apache-2.0,"The Rand Project Developers, The Rust Project Developers"
rand_chacha,https://github.com/rust-random/rand,MIT OR Apache-2.0,"The Rand Project Developers, The Rust Project Developers, The CryptoCorrosion Contributors"
rand_distr,https://github.com/rust-random/rand,MIT OR Apache-2.0,The Rand Project Developers
rand_hc,https://github.com/rust-random/rand,MIT OR Apache-2.0,The Rand Project Developers
rand_xorshift,https://github.com/rust-random/rngs,MIT OR Apache-2.0,"The Rand Project Developers, The Rust Project Developers"
ratatui,https://github.com/ratatui-org/ratatui,MIT,"Florian Dehau <<EMAIL>>, The Ratatui Developers"
raw-cpuid,https://github.com/gz/rust-cpuid,MIT,Gerd Zellweger <<EMAIL>>
raw-window-handle,https://github.com/rust-windowing/raw-window-handle,MIT OR Apache-2.0 OR Zlib,Osspial <<EMAIL>>
rdkafka,https://github.com/fede1024/rust-rdkafka,MIT,Federico Giraud <<EMAIL>>
redis,https://github.com/redis-rs/redis-rs,BSD-3-Clause,The redis Authors
redox_syscall,https://gitlab.redox-os.org/redox-os/syscall,MIT,Jeremy Soller <<EMAIL>>
redox_users,https://gitlab.redox-os.org/redox-os/users,MIT,"Jose Narvaez <<EMAIL>>, Wesley Hershberger <<EMAIL>>"
regex,https://github.com/rust-lang/regex,MIT OR Apache-2.0,"The Rust Project Developers, Andrew Gallant <<EMAIL>>"
regex-automata,https://github.com/BurntSushi/regex-automata,Unlicense OR MIT,Andrew Gallant <<EMAIL>>
regex-automata,https://github.com/rust-lang/regex/tree/master/regex-automata,MIT OR Apache-2.0,"The Rust Project Developers, Andrew Gallant <<EMAIL>>"
regex-lite,https://github.com/rust-lang/regex/tree/master/regex-lite,MIT OR Apache-2.0,"The Rust Project Developers, Andrew Gallant <<EMAIL>>"
regex-syntax,https://github.com/rust-lang/regex,MIT OR Apache-2.0,The Rust Project Developers
regex-syntax,https://github.com/rust-lang/regex/tree/master/regex-syntax,MIT OR Apache-2.0,"The Rust Project Developers, Andrew Gallant <<EMAIL>>"
rend,https://github.com/djkoloski/rend,MIT,David Koloski <<EMAIL>>
reqwest,https://github.com/seanmonstar/reqwest,MIT OR Apache-2.0,Sean McArthur <<EMAIL>>
resolv-conf,http://github.com/tailhook/resolv-conf,MIT OR Apache-2.0,<EMAIL>
rfc6979,https://github.com/RustCrypto/signatures/tree/master/rfc6979,Apache-2.0 OR MIT,RustCrypto Developers
ring,https://github.com/briansmith/ring,ISC AND Custom,Brian Smith <<EMAIL>>
rkyv,https://github.com/rkyv/rkyv,MIT,David Koloski <<EMAIL>>
rle-decode-fast,https://github.com/WanzenBug/rle-decode-helper,MIT OR Apache-2.0,Moritz Wanzenböck <<EMAIL>>
rmp,https://github.com/3Hren/msgpack-rust,MIT,Evgeny Safronov <<EMAIL>>
rmp-serde,https://github.com/3Hren/msgpack-rust,MIT,Evgeny Safronov <<EMAIL>>
rmpv,https://github.com/3Hren/msgpack-rust,MIT,Evgeny Safronov <<EMAIL>>
roaring,https://github.com/RoaringBitmap/roaring-rs,MIT OR Apache-2.0,"Wim Looman <<EMAIL>>, Kerollmops <<EMAIL>>"
roxmltree,https://github.com/RazrFalcon/roxmltree,MIT OR Apache-2.0,Yevhenii Reizner <<EMAIL>>
rsa,https://github.com/RustCrypto/RSA,MIT OR Apache-2.0,"RustCrypto Developers, dignifiedquire <<EMAIL>>"
rumqttc,https://github.com/bytebeamio/rumqtt,Apache-2.0,tekjar <<EMAIL>>
rust_decimal,https://github.com/paupino/rust-decimal,MIT,Paul Mason <<EMAIL>>
rustc-demangle,https://github.com/alexcrichton/rustc-demangle,MIT OR Apache-2.0,Alex Crichton <<EMAIL>>
rustc-hash,https://github.com/rust-lang-nursery/rustc-hash,Apache-2.0 OR MIT,The Rust Project Developers
rustc_version,https://github.com/Kimundi/rustc-version-rs,MIT OR Apache-2.0,Marvin Löbel <<EMAIL>>
rustc_version_runtime,https://github.com/seppo0010/rustc-version-runtime-rs,MIT,Sebastian Waisbrot <<EMAIL>>
rustix,https://github.com/bytecodealliance/rustix,Apache-2.0 WITH LLVM-exception OR Apache-2.0 OR MIT,"Dan Gohman <<EMAIL>>, Jakub Konka <<EMAIL>>"
rustls,https://github.com/rustls/rustls,Apache-2.0 OR ISC OR MIT,The rustls Authors
rustls-native-certs,https://github.com/ctz/rustls-native-certs,Apache-2.0 OR ISC OR MIT,The rustls-native-certs Authors
rustls-native-certs,https://github.com/rustls/rustls-native-certs,Apache-2.0 OR ISC OR MIT,The rustls-native-certs Authors
rustls-pemfile,https://github.com/rustls/pemfile,Apache-2.0 OR ISC OR MIT,The rustls-pemfile Authors
rustls-pki-types,https://github.com/rustls/pki-types,MIT OR Apache-2.0,The rustls-pki-types Authors
rustls-webpki,https://github.com/rustls/webpki,ISC,The rustls-webpki Authors
rustversion,https://github.com/dtolnay/rustversion,MIT OR Apache-2.0,David Tolnay <<EMAIL>>
rusty-fork,https://github.com/altsysrq/rusty-fork,MIT OR Apache-2.0,Jason Lingle
rustyline,https://github.com/kkawakam/rustyline,MIT,Katsu Kawakami <<EMAIL>>
ryu,https://github.com/dtolnay/ryu,Apache-2.0 OR BSL-1.0,David Tolnay <<EMAIL>>
salsa20,https://github.com/RustCrypto/stream-ciphers,MIT OR Apache-2.0,RustCrypto Developers
same-file,https://github.com/BurntSushi/same-file,Unlicense OR MIT,Andrew Gallant <<EMAIL>>
sasl2-sys,https://github.com/MaterializeInc/rust-sasl,Apache-2.0,"Materialize, Inc."
scan_fmt,https://github.com/wlentz/scan_fmt,MIT,wlentz
schannel,https://github.com/steffengy/schannel-rs,MIT,"Steven Fackler <<EMAIL>>, Steffen Butzer <<EMAIL>>"
scoped-tls,https://github.com/alexcrichton/scoped-tls,MIT OR Apache-2.0,Alex Crichton <<EMAIL>>
scopeguard,https://github.com/bluss/scopeguard,MIT OR Apache-2.0,bluss
sct,https://github.com/rustls/sct.rs,Apache-2.0 OR ISC OR MIT,Joseph Birr-Pixton <<EMAIL>>
seahash,https://gitlab.redox-os.org/redox-os/seahash,MIT,"ticki <<EMAIL>>, Tom Almeida <<EMAIL>>"
sec1,https://github.com/RustCrypto/formats/tree/master/sec1,Apache-2.0 OR MIT,RustCrypto Developers
secrecy,https://github.com/iqlusioninc/crates/tree/main/secrecy,Apache-2.0 OR MIT,Tony Arcieri <<EMAIL>>
security-framework,https://github.com/kornelski/rust-security-framework,MIT OR Apache-2.0,"Steven Fackler <<EMAIL>>, Kornel <<EMAIL>>"
semver,https://github.com/dtolnay/semver,MIT OR Apache-2.0,David Tolnay <<EMAIL>>
semver,https://github.com/steveklabnik/semver,MIT OR Apache-2.0,"Steve Klabnik <<EMAIL>>, The Rust Project Developers"
semver-parser,https://github.com/steveklabnik/semver-parser,MIT OR Apache-2.0,Steve Klabnik <<EMAIL>>
serde,https://github.com/serde-rs/serde,MIT OR Apache-2.0,"Erick Tryzelaar <<EMAIL>>, David Tolnay <<EMAIL>>"
serde-toml-merge,https://github.com/jdrouet/serde-toml-merge,MIT,Jeremie Drouet <<EMAIL>>
serde-value,https://github.com/arcnmx/serde-value,MIT,arcnmx
serde_bytes,https://github.com/serde-rs/bytes,MIT OR Apache-2.0,David Tolnay <<EMAIL>>
serde_json,https://github.com/serde-rs/json,MIT OR Apache-2.0,"Erick Tryzelaar <<EMAIL>>, David Tolnay <<EMAIL>>"
serde_nanos,https://github.com/caspervonb/serde_nanos,MIT OR Apache-2.0,Casper Beyer <<EMAIL>>
serde_path_to_error,https://github.com/dtolnay/path-to-error,MIT OR Apache-2.0,David Tolnay <<EMAIL>>
serde_plain,https://github.com/mitsuhiko/serde-plain,MIT OR Apache-2.0,Armin Ronacher <<EMAIL>>
serde_qs,https://github.com/samscott89/serde_qs,MIT OR Apache-2.0,Sam Scott <<EMAIL>>
serde_repr,https://github.com/dtolnay/serde-repr,MIT OR Apache-2.0,David Tolnay <<EMAIL>>
serde_spanned,https://github.com/toml-rs/toml,MIT OR Apache-2.0,The serde_spanned Authors
serde_urlencoded,https://github.com/nox/serde_urlencoded,MIT OR Apache-2.0,Anthony Ramine <<EMAIL>>
serde_with,https://github.com/jonasbb/serde_with,MIT OR Apache-2.0,"Jonas Bushart, Marcin Kaźmierczak"
serde_with_macros,https://github.com/jonasbb/serde_with,MIT OR Apache-2.0,Jonas Bushart
serde_yaml,https://github.com/dtolnay/serde-yaml,MIT OR Apache-2.0,David Tolnay <<EMAIL>>
sha-1,https://github.com/RustCrypto/hashes,MIT OR Apache-2.0,RustCrypto Developers
sha1,https://github.com/RustCrypto/hashes,MIT OR Apache-2.0,RustCrypto Developers
sha2,https://github.com/RustCrypto/hashes,MIT OR Apache-2.0,RustCrypto Developers
sha3,https://github.com/RustCrypto/hashes,MIT OR Apache-2.0,RustCrypto Developers
sharded-slab,https://github.com/hawkw/sharded-slab,MIT,Eliza Weisman <<EMAIL>>
signal-hook,https://github.com/vorner/signal-hook,Apache-2.0 OR MIT,"Michal 'vorner' Vaner <<EMAIL>>, Thomas Himmelstoss <<EMAIL>>"
signal-hook-registry,https://github.com/vorner/signal-hook,Apache-2.0 OR MIT,"Michal 'vorner' Vaner <<EMAIL>>, Masaki Hara <<EMAIL>>"
signatory,https://github.com/iqlusioninc/crates/tree/main/signatory,Apache-2.0 OR MIT,Tony Arcieri <<EMAIL>>
signature,https://github.com/RustCrypto/traits/tree/master/signature,Apache-2.0 OR MIT,RustCrypto Developers
simdutf8,https://github.com/rusticstuff/simdutf8,MIT OR Apache-2.0,Hans Kratz <<EMAIL>>
simpl,https://github.com/durch/simplerr,MIT,Drazen Urch <<EMAIL>>
siphasher,https://github.com/jedisct1/rust-siphash,MIT OR Apache-2.0,Frank Denis <<EMAIL>>
sketches-ddsketch,https://github.com/mheffner/rust-sketches-ddsketch,Apache-2.0,Mike Heffner <<EMAIL>>
slab,https://github.com/tokio-rs/slab,MIT,Carl Lerche <<EMAIL>>
smallvec,https://github.com/servo/rust-smallvec,MIT OR Apache-2.0,The Servo Project Developers
smol,https://github.com/smol-rs/smol,Apache-2.0 OR MIT,Stjepan Glavina <<EMAIL>>
smpl_jwt,https://github.com/durch/rust-jwt,MIT,Drazen Urch <<EMAIL>>
snafu,https://github.com/shepmaster/snafu,MIT OR Apache-2.0,Jake Goulding <<EMAIL>>
snap,https://github.com/BurntSushi/rust-snappy,BSD-3-Clause,Andrew Gallant <<EMAIL>>
socket2,https://github.com/rust-lang/socket2,MIT OR Apache-2.0,"Alex Crichton <<EMAIL>>, Thomas de Zeeuw <<EMAIL>>"
spin,https://github.com/mvdnes/spin-rs,MIT,"Mathijs van de Nes <*********************>, John Ericson <******************>"
spin,https://github.com/mvdnes/spin-rs,MIT,"Mathijs van de Nes <*********************>, John Ericson <******************>, Joshua Barretto <<EMAIL>>"
spki,https://github.com/RustCrypto/formats/tree/master/spki,Apache-2.0 OR MIT,RustCrypto Developers
stability,https://github.com/sagebind/stability,MIT,Stephen M. Coakley <<EMAIL>>
static_assertions,https://github.com/nvzqz/static-assertions-rs,MIT OR Apache-2.0,Nikolai Vazquez
static_assertions_next,https://github.com/scuffletv/static-assertions,MIT OR Apache-2.0,Nikolai Vazquez
stream-cancel,https://github.com/jonhoo/stream-cancel,MIT OR Apache-2.0,Jon Gjengset <<EMAIL>>
stringprep,https://github.com/sfackler/rust-stringprep,MIT OR Apache-2.0,Steven Fackler <<EMAIL>>
strip-ansi-escapes,https://github.com/luser/strip-ansi-escapes,Apache-2.0 OR MIT,Ted Mielczarek <<EMAIL>>
strsim,https://github.com/dguo/strsim-rs,MIT,Danny Guo <<EMAIL>>
strsim,https://github.com/dguo/strsim-rs,MIT,Danny Guo <<EMAIL>>
strsim,https://github.com/rapidfuzz/strsim-rs,MIT,"Danny Guo <<EMAIL>>, maxbachmann <<EMAIL>>"
structopt,https://github.com/TeXitoi/structopt,Apache-2.0 OR MIT,"Guillaume Pinot <<EMAIL>>, others"
structopt-derive,https://github.com/TeXitoi/structopt,Apache-2.0 OR MIT,Guillaume Pinot <<EMAIL>>
strum,https://github.com/Peternator7/strum,MIT,Peter Glotfelty <<EMAIL>>
subtle,https://github.com/dalek-cryptography/subtle,BSD-3-Clause,"Isis Lovecruft <<EMAIL>>, Henry de Valence <<EMAIL>>"
syn,https://github.com/dtolnay/syn,MIT OR Apache-2.0,David Tolnay <<EMAIL>>
syn_derive,https://github.com/Kyuuhachi/syn_derive,MIT OR Apache-2.0,Kyuuhachi <<EMAIL>>
sync_wrapper,https://github.com/Actyx/sync_wrapper,Apache-2.0,Actyx AG <<EMAIL>>
syslog,https://github.com/Geal/rust-syslog,MIT,<EMAIL>
syslog_loose,https://github.com/FungusHumungus/syslog-loose,MIT,Stephen Wakely <<EMAIL>>
system-configuration,https://github.com/mullvad/system-configuration-rs,MIT OR Apache-2.0,Mullvad VPN
take_mut,https://github.com/Sgeo/take_mut,MIT,Sgeo <<EMAIL>>
tap,https://github.com/myrrlyn/tap,MIT,"Elliott Linder <<EMAIL>>, myrrlyn <<EMAIL>>"
tcp-stream,https://github.com/amqp-rs/tcp-stream,BSD-2-Clause,Marc-Antoine Perennou <<EMAIL>>
tempfile,https://github.com/Stebalien/tempfile,MIT OR Apache-2.0,"Steven Allen <<EMAIL>>, The Rust Project Developers, Ashley Mannix <<EMAIL>>, Jason White <<EMAIL>>"
term,https://github.com/Stebalien/term,MIT OR Apache-2.0,"The Rust Project Developers, Steven Allen"
termcolor,https://github.com/BurntSushi/termcolor,Unlicense OR MIT,Andrew Gallant <<EMAIL>>
terminal_size,https://github.com/eminence/terminal-size,MIT OR Apache-2.0,Andrew Chin <<EMAIL>>
textwrap,https://github.com/mgeisler/textwrap,MIT,Martin Geisler <<EMAIL>>
thiserror,https://github.com/dtolnay/thiserror,MIT OR Apache-2.0,David Tolnay <<EMAIL>>
thread_local,https://github.com/Amanieu/thread_local-rs,MIT OR Apache-2.0,Amanieu d'Antras <<EMAIL>>
tikv-jemalloc-sys,https://github.com/tikv/jemallocator,MIT OR Apache-2.0,"Alex Crichton <<EMAIL>>, Gonzalo Brito Gadeschi <<EMAIL>>, The TiKV Project Developers"
tikv-jemallocator,https://github.com/tikv/jemallocator,MIT OR Apache-2.0,"Alex Crichton <<EMAIL>>, Gonzalo Brito Gadeschi <<EMAIL>>, Simon Sapin <<EMAIL>>, Steven Fackler <<EMAIL>>, The TiKV Project Developers"
time,https://github.com/time-rs/time,MIT OR Apache-2.0,"Jacob Pratt <<EMAIL>>, Time contributors"
tinyvec,https://github.com/Lokathor/tinyvec,Zlib OR Apache-2.0 OR MIT,Lokathor <<EMAIL>>
tinyvec_macros,https://github.com/Soveu/tinyvec_macros,MIT OR Apache-2.0 OR Zlib,Soveu <<EMAIL>>
tokio,https://github.com/tokio-rs/tokio,MIT,Tokio Contributors <<EMAIL>>
tokio-io,https://github.com/tokio-rs/tokio,MIT,Carl Lerche <<EMAIL>>
tokio-io-timeout,https://github.com/sfackler/tokio-io-timeout,MIT OR Apache-2.0,Steven Fackler <<EMAIL>>
tokio-native-tls,https://github.com/tokio-rs/tls,MIT,Tokio Contributors <<EMAIL>>
tokio-openssl,https://github.com/tokio-rs/tokio-openssl,MIT OR Apache-2.0,Alex Crichton <<EMAIL>>
tokio-postgres,https://github.com/sfackler/rust-postgres,MIT OR Apache-2.0,Steven Fackler <<EMAIL>>
tokio-retry,https://github.com/srijs/rust-tokio-retry,MIT,Sam Rijs <<EMAIL>>
tokio-rustls,https://github.com/rustls/tokio-rustls,MIT OR Apache-2.0,The tokio-rustls Authors
tokio-tungstenite,https://github.com/snapview/tokio-tungstenite,MIT,"Daniel Abramov <<EMAIL>>, Alexey Galakhov <<EMAIL>>"
toml,https://github.com/toml-rs/toml,MIT OR Apache-2.0,Alex Crichton <<EMAIL>>
toml_edit,https://github.com/toml-rs/toml,MIT OR Apache-2.0,"Andronik Ordian <<EMAIL>>, Ed Page <<EMAIL>>"
tonic,https://github.com/hyperium/tonic,MIT,Lucio Franco <<EMAIL>>
tower,https://github.com/tower-rs/tower,MIT,Tower Maintainers <<EMAIL>>
tower-http,https://github.com/tower-rs/tower-http,MIT,Tower Maintainers <<EMAIL>>
tracing,https://github.com/tokio-rs/tracing,MIT,"Eliza Weisman <<EMAIL>>, Tokio Contributors <<EMAIL>>"
tracing-attributes,https://github.com/tokio-rs/tracing,MIT,"Tokio Contributors <<EMAIL>>, Eliza Weisman <<EMAIL>>, David Barsky <<EMAIL>>"
tracing-core,https://github.com/tokio-rs/tracing,MIT,Tokio Contributors <<EMAIL>>
tracing-log,https://github.com/tokio-rs/tracing,MIT,Tokio Contributors <<EMAIL>>
tracing-serde,https://github.com/tokio-rs/tracing,MIT,Tokio Contributors <<EMAIL>>
tracing-subscriber,https://github.com/tokio-rs/tracing,MIT,"Eliza Weisman <<EMAIL>>, David Barsky <<EMAIL>>, Tokio Contributors <<EMAIL>>"
tracing-tower,https://github.com/tokio-rs/tracing,MIT,Eliza Weisman <<EMAIL>>
treediff,https://github.com/Byron/treediff-rs,MIT OR Apache-2.0,Sebastian Thiel <<EMAIL>>
trust-dns-proto,https://github.com/bluejekyll/trust-dns,MIT OR Apache-2.0,Benjamin Fry <<EMAIL>>
trust-dns-resolver,https://github.com/bluejekyll/trust-dns,MIT OR Apache-2.0,Benjamin Fry <<EMAIL>>
try-lock,https://github.com/seanmonstar/try-lock,MIT,Sean McArthur <<EMAIL>>
tungstenite,https://github.com/snapview/tungstenite-rs,MIT OR Apache-2.0,"Alexey Galakhov, Daniel Abramov"
twox-hash,https://github.com/shepmaster/twox-hash,MIT,Jake Goulding <<EMAIL>>
typed-builder,https://github.com/idanarye/rust-typed-builder,MIT OR Apache-2.0,"IdanArye <<EMAIL>>, Chris Morgan <<EMAIL>>"
typenum,https://github.com/paholg/typenum,MIT OR Apache-2.0,"Paho Lurie-Gregg <<EMAIL>>, Andre Bogus <<EMAIL>>"
typetag,https://github.com/dtolnay/typetag,MIT OR Apache-2.0,David Tolnay <<EMAIL>>
tz-rs,https://github.com/x-hgg-x/tz-rs,MIT OR Apache-2.0,x-hgg-x
uaparser,https://github.com/davidarmstronglewis/uap-rs,MIT,David Lewis
ucd-trie,https://github.com/BurntSushi/ucd-generate,MIT OR Apache-2.0,Andrew Gallant <<EMAIL>>
unarray,https://github.com/cameron1024/unarray,MIT OR Apache-2.0,The unarray Authors
unicase,https://github.com/seanmonstar/unicase,MIT OR Apache-2.0,Sean McArthur <<EMAIL>>
unicode-bidi,https://github.com/servo/unicode-bidi,MIT OR Apache-2.0,The Servo Project Developers
unicode-ident,https://github.com/dtolnay/unicode-ident,(MIT OR Apache-2.0) AND Unicode-DFS-2016,David Tolnay <<EMAIL>>
unicode-normalization,https://github.com/unicode-rs/unicode-normalization,MIT OR Apache-2.0,"kwantam <<EMAIL>>, Manish Goregaokar <<EMAIL>>"
unicode-segmentation,https://github.com/unicode-rs/unicode-segmentation,MIT OR Apache-2.0,"kwantam <<EMAIL>>, Manish Goregaokar <<EMAIL>>"
unicode-width,https://github.com/unicode-rs/unicode-width,MIT OR Apache-2.0,"kwantam <<EMAIL>>, Manish Goregaokar <<EMAIL>>"
universal-hash,https://github.com/RustCrypto/traits,MIT OR Apache-2.0,RustCrypto Developers
unreachable,https://github.com/reem/rust-unreachable,MIT  OR  Apache-2.0,Jonathan Reem <<EMAIL>>
unsafe-libyaml,https://github.com/dtolnay/unsafe-libyaml,MIT,David Tolnay <<EMAIL>>
untrusted,https://github.com/briansmith/untrusted,ISC,Brian Smith <<EMAIL>>
uom,https://github.com/iliekturtles/uom,Apache-2.0 OR MIT,Mike Boutin <<EMAIL>>
url,https://github.com/servo/rust-url,MIT OR Apache-2.0,The rust-url developers
urlencoding,https://github.com/kornelski/rust_urlencoding,MIT,"Kornel <<EMAIL>>, Bertram Truong <<EMAIL>>"
utf-8,https://github.com/SimonSapin/rust-utf8,MIT OR Apache-2.0,Simon Sapin <<EMAIL>>
utf8-width,https://github.com/magiclen/utf8-width,MIT,Magic Len <<EMAIL>>
uuid,https://github.com/uuid-rs/uuid,Apache-2.0 OR MIT,"Ashley Mannix<<EMAIL>>, Christopher Armstrong, Dylan DPC<<EMAIL>>, Hunar Roop Kahlon<<EMAIL>>"
valuable,https://github.com/tokio-rs/valuable,MIT,The valuable Authors
vec_map,https://github.com/contain-rs/vec-map,MIT OR Apache-2.0,"Alex Crichton <<EMAIL>>, Jorge Aparicio <<EMAIL>>, Alexis Beingessner <<EMAIL>>, Brian Anderson <>, tbu- <>, Manish Goregaokar <>, Aaron Turon <<EMAIL>>, Adolfo Ochagavía <>, Niko Matsakis <>, Steven Fackler <>, Chase Southwood <<EMAIL>>, Eduard Burtescu <>, Florian Wilkens <>, Félix Raimundo <>, Tibor Benke <>, Markus Siemens <<EMAIL>>, Josh Branchaud <<EMAIL>>, Huon Wilson <<EMAIL>>, Corey Farwell <<EMAIL>>, Aaron Liblong <>, Nick Cameron <<EMAIL>>, Patrick Walton <<EMAIL>>, Felix S Klock II <>, Andrew Paseltiner <<EMAIL>>, Sean McArthur <<EMAIL>>, Vadim Petrochenkov <>"
void,https://github.com/reem/rust-void,MIT,Jonathan Reem <<EMAIL>>
vrl,https://github.com/vectordotdev/vrl,MPL-2.0,Vector Contributors <<EMAIL>>
vsimd,https://github.com/Nugine/simd,MIT,The vsimd Authors
vte,https://github.com/alacritty/vte,Apache-2.0 OR MIT,"Joe Wilm <<EMAIL>>, Christian Duerr <<EMAIL>>"
vte_generate_state_changes,https://github.com/jwilm/vte,Apache-2.0 OR MIT,Christian Duerr <<EMAIL>>
wait-timeout,https://github.com/alexcrichton/wait-timeout,MIT OR Apache-2.0,Alex Crichton <<EMAIL>>
waker-fn,https://github.com/smol-rs/waker-fn,Apache-2.0 OR MIT,Stjepan Glavina <<EMAIL>>
walkdir,https://github.com/BurntSushi/walkdir,Unlicense OR MIT,Andrew Gallant <<EMAIL>>
want,https://github.com/seanmonstar/want,MIT,Sean McArthur <<EMAIL>>
warp,https://github.com/seanmonstar/warp,MIT,Sean McArthur <<EMAIL>>
wasi,https://github.com/bytecodealliance/wasi,Apache-2.0 WITH LLVM-exception OR Apache-2.0 OR MIT,The Cranelift Project Developers
wasite,https://github.com/ardaku/wasite,Apache-2.0 OR BSL-1.0 OR MIT,The wasite Authors
wasm-bindgen,https://github.com/rustwasm/wasm-bindgen,MIT OR Apache-2.0,The wasm-bindgen Developers
wasm-bindgen-backend,https://github.com/rustwasm/wasm-bindgen/tree/master/crates/backend,MIT OR Apache-2.0,The wasm-bindgen Developers
wasm-bindgen-futures,https://github.com/rustwasm/wasm-bindgen/tree/master/crates/futures,MIT OR Apache-2.0,The wasm-bindgen Developers
wasm-bindgen-macro,https://github.com/rustwasm/wasm-bindgen/tree/master/crates/macro,MIT OR Apache-2.0,The wasm-bindgen Developers
wasm-bindgen-macro-support,https://github.com/rustwasm/wasm-bindgen/tree/master/crates/macro-support,MIT OR Apache-2.0,The wasm-bindgen Developers
wasm-bindgen-shared,https://github.com/rustwasm/wasm-bindgen/tree/master/crates/shared,MIT OR Apache-2.0,The wasm-bindgen Developers
wasm-streams,https://github.com/MattiasBuelens/wasm-streams,MIT OR Apache-2.0,Mattias Buelens <<EMAIL>>
web-sys,https://github.com/rustwasm/wasm-bindgen/tree/master/crates/web-sys,MIT OR Apache-2.0,The wasm-bindgen Developers
webbrowser,https://github.com/amodm/webbrowser-rs,MIT OR Apache-2.0,Amod Malviya @amodm
webpki-roots,https://github.com/rustls/webpki-roots,MPL-2.0,The webpki-roots Authors
whoami,https://github.com/ardaku/whoami,Apache-2.0 OR BSL-1.0 OR MIT,The whoami Authors
widestring,https://github.com/starkat99/widestring-rs,MIT OR Apache-2.0,Kathryn Long <<EMAIL>>
widestring,https://github.com/starkat99/widestring-rs,MIT OR Apache-2.0,The widestring Authors
winapi,https://github.com/retep998/winapi-rs,MIT OR Apache-2.0,Peter Atashian <<EMAIL>>
winapi-util,https://github.com/BurntSushi/winapi-util,Unlicense OR MIT,Andrew Gallant <<EMAIL>>
windows-core,https://github.com/microsoft/windows-rs,MIT OR Apache-2.0,Microsoft
windows-service,https://github.com/mullvad/windows-service-rs,MIT OR Apache-2.0,Mullvad VPN
windows-sys,https://github.com/microsoft/windows-rs,MIT OR Apache-2.0,Microsoft
windows-targets,https://github.com/microsoft/windows-rs,MIT OR Apache-2.0,Microsoft
windows_aarch64_gnullvm,https://github.com/microsoft/windows-rs,MIT OR Apache-2.0,Microsoft
windows_aarch64_msvc,https://github.com/microsoft/windows-rs,MIT OR Apache-2.0,Microsoft
windows_i686_gnu,https://github.com/microsoft/windows-rs,MIT OR Apache-2.0,Microsoft
windows_i686_msvc,https://github.com/microsoft/windows-rs,MIT OR Apache-2.0,Microsoft
windows_x86_64_gnu,https://github.com/microsoft/windows-rs,MIT OR Apache-2.0,Microsoft
windows_x86_64_gnullvm,https://github.com/microsoft/windows-rs,MIT OR Apache-2.0,Microsoft
windows_x86_64_msvc,https://github.com/microsoft/windows-rs,MIT OR Apache-2.0,Microsoft
winnow,https://github.com/winnow-rs/winnow,MIT,The winnow Authors
winreg,https://github.com/gentoo90/winreg-rs,MIT,Igor Shaula <<EMAIL>>
woothee,https://github.com/woothee/woothee-rust,Apache-2.0,hhatto <<EMAIL>>
wyz,https://github.com/myrrlyn/wyz,MIT,myrrlyn <<EMAIL>>
xmlparser,https://github.com/RazrFalcon/xmlparser,MIT OR Apache-2.0,Yevhenii Reizner <<EMAIL>>
yaml-rust,https://github.com/chyh1990/yaml-rust,MIT OR Apache-2.0,Yuheng Chen <<EMAIL>>
zerocopy,https://github.com/google/zerocopy,BSD-2-Clause OR Apache-2.0 OR MIT,Joshua Liebow-Feeser <<EMAIL>>
zeroize,https://github.com/RustCrypto/utils/tree/master/zeroize,Apache-2.0 OR MIT,The RustCrypto Project Developers
zstd,https://github.com/gyscos/zstd-rs,MIT,Alexandre Bury <<EMAIL>>
zstd-safe,https://github.com/gyscos/zstd-rs,MIT OR Apache-2.0,Alexandre Bury <<EMAIL>>
zstd-sys,https://github.com/gyscos/zstd-rs,MIT OR Apache-2.0,Alexandre Bury <<EMAIL>>
