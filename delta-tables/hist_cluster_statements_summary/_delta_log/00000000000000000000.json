{"protocol":{"minReaderVersion":3,"minWriterVersion":7,"readerFeatures":["timestampNtz"],"writerFeatures":["timestampNtz"]}}
{"metaData":{"id":"d8699aa7-5c38-437b-a754-4631c1e1280e","name":null,"description":null,"format":{"provider":"parquet","options":{}},"schemaString":"{\"type\":\"struct\",\"fields\":[{\"name\":\"_vector_table\",\"type\":\"string\",\"nullable\":false,\"metadata\":{}},{\"name\":\"_vector_source_table\",\"type\":\"string\",\"nullable\":false,\"metadata\":{}},{\"name\":\"_vector_source_schema\",\"type\":\"string\",\"nullable\":false,\"metadata\":{}},{\"name\":\"_vector_instance\",\"type\":\"string\",\"nullable\":false,\"metadata\":{}},{\"name\":\"_vector_timestamp\",\"type\":\"string\",\"nullable\":false,\"metadata\":{}},{\"name\":\"AVG_AFFECTED_ROWS\",\"type\":\"double\",\"nullable\":true,\"metadata\":{}},{\"name\":\"AVG_BACKOFF_TIME\",\"type\":\"long\",\"nullable\":true,\"metadata\":{}},{\"name\":\"AVG_BACKOFF_TOTAL_TIME\",\"type\":\"long\",\"nullable\":true,\"metadata\":{}},{\"name\":\"AVG_COMMIT_BACKOFF_TIME\",\"type\":\"long\",\"nullable\":true,\"metadata\":{}},{\"name\":\"AVG_COMMIT_TIME\",\"type\":\"long\",\"nullable\":true,\"metadata\":{}},{\"name\":\"AVG_COMPILE_LATENCY\",\"type\":\"long\",\"nullable\":true,\"metadata\":{}},{\"name\":\"AVG_DISK\",\"type\":\"long\",\"nullable\":true,\"metadata\":{}},{\"name\":\"AVG_GET_COMMIT_TS_TIME\",\"type\":\"long\",\"nullable\":true,\"metadata\":{}},{\"name\":\"AVG_KV_TIME\",\"type\":\"long\",\"nullable\":true,\"metadata\":{}},{\"name\":\"AVG_LATENCY\",\"type\":\"long\",\"nullable\":true,\"metadata\":{}},{\"name\":\"AVG_LOCAL_LATCH_WAIT_TIME\",\"type\":\"long\",\"nullable\":true,\"metadata\":{}},{\"name\":\"AVG_MEM\",\"type\":\"long\",\"nullable\":true,\"metadata\":{}},{\"name\":\"AVG_PARSE_LATENCY\",\"type\":\"long\",\"nullable\":true,\"metadata\":{}},{\"name\":\"AVG_PD_TIME\",\"type\":\"long\",\"nullable\":true,\"metadata\":{}},{\"name\":\"AVG_PREWRITE_REGIONS\",\"type\":\"double\",\"nullable\":true,\"metadata\":{}},{\"name\":\"AVG_PREWRITE_TIME\",\"type\":\"long\",\"nullable\":true,\"metadata\":{}},{\"name\":\"AVG_PROCESSED_KEYS\",\"type\":\"long\",\"nullable\":true,\"metadata\":{}},{\"name\":\"AVG_PROCESS_TIME\",\"type\":\"long\",\"nullable\":true,\"metadata\":{}},{\"name\":\"AVG_QUEUED_RC_TIME\",\"type\":\"long\",\"nullable\":true,\"metadata\":{}},{\"name\":\"AVG_REQUEST_UNIT_READ\",\"type\":\"double\",\"nullable\":true,\"metadata\":{}},{\"name\":\"AVG_REQUEST_UNIT_WRITE\",\"type\":\"double\",\"nullable\":true,\"metadata\":{}},{\"name\":\"AVG_RESOLVE_LOCK_TIME\",\"type\":\"long\",\"nullable\":true,\"metadata\":{}},{\"name\":\"AVG_RESULT_ROWS\",\"type\":\"long\",\"nullable\":true,\"metadata\":{}},{\"name\":\"AVG_ROCKSDB_BLOCK_CACHE_HIT_COUNT\",\"type\":\"double\",\"nullable\":true,\"metadata\":{}},{\"name\":\"AVG_ROCKSDB_BLOCK_READ_BYTE\",\"type\":\"double\",\"nullable\":true,\"metadata\":{}},{\"name\":\"AVG_ROCKSDB_BLOCK_READ_COUNT\",\"type\":\"double\",\"nullable\":true,\"metadata\":{}},{\"name\":\"AVG_ROCKSDB_DELETE_SKIPPED_COUNT\",\"type\":\"double\",\"nullable\":true,\"metadata\":{}},{\"name\":\"AVG_ROCKSDB_KEY_SKIPPED_COUNT\",\"type\":\"double\",\"nullable\":true,\"metadata\":{}},{\"name\":\"AVG_TIDB_CPU_TIME\",\"type\":\"long\",\"nullable\":true,\"metadata\":{}},{\"name\":\"AVG_TIKV_CPU_TIME\",\"type\":\"long\",\"nullable\":true,\"metadata\":{}},{\"name\":\"AVG_TOTAL_KEYS\",\"type\":\"long\",\"nullable\":true,\"metadata\":{}},{\"name\":\"AVG_TXN_RETRY\",\"type\":\"double\",\"nullable\":true,\"metadata\":{}},{\"name\":\"AVG_WAIT_TIME\",\"type\":\"long\",\"nullable\":true,\"metadata\":{}},{\"name\":\"AVG_WRITE_KEYS\",\"type\":\"double\",\"nullable\":true,\"metadata\":{}},{\"name\":\"AVG_WRITE_SIZE\",\"type\":\"double\",\"nullable\":true,\"metadata\":{}},{\"name\":\"AVG_WRITE_SQL_RESP_TIME\",\"type\":\"long\",\"nullable\":true,\"metadata\":{}},{\"name\":\"BACKOFF_TYPES\",\"type\":\"string\",\"nullable\":true,\"metadata\":{}},{\"name\":\"BINARY_PLAN\",\"type\":\"string\",\"nullable\":true,\"metadata\":{}},{\"name\":\"BINDING_DIGEST\",\"type\":\"string\",\"nullable\":true,\"metadata\":{}},{\"name\":\"BINDING_DIGEST_TEXT\",\"type\":\"string\",\"nullable\":true,\"metadata\":{}},{\"name\":\"CHARSET\",\"type\":\"string\",\"nullable\":true,\"metadata\":{}},{\"name\":\"COLLATION\",\"type\":\"string\",\"nullable\":true,\"metadata\":{}},{\"name\":\"DIGEST\",\"type\":\"string\",\"nullable\":true,\"metadata\":{}},{\"name\":\"DIGEST_TEXT\",\"type\":\"string\",\"nullable\":true,\"metadata\":{}},{\"name\":\"EXEC_COUNT\",\"type\":\"long\",\"nullable\":true,\"metadata\":{}},{\"name\":\"FIRST_SEEN\",\"type\":\"timestamp_ntz\",\"nullable\":true,\"metadata\":{}},{\"name\":\"INDEX_NAMES\",\"type\":\"string\",\"nullable\":true,\"metadata\":{}},{\"name\":\"INSTANCE\",\"type\":\"string\",\"nullable\":true,\"metadata\":{}},{\"name\":\"LAST_SEEN\",\"type\":\"timestamp_ntz\",\"nullable\":true,\"metadata\":{}},{\"name\":\"MAX_BACKOFF_TIME\",\"type\":\"long\",\"nullable\":true,\"metadata\":{}},{\"name\":\"MAX_COMMIT_BACKOFF_TIME\",\"type\":\"long\",\"nullable\":true,\"metadata\":{}},{\"name\":\"MAX_COMMIT_TIME\",\"type\":\"long\",\"nullable\":true,\"metadata\":{}},{\"name\":\"MAX_COMPILE_LATENCY\",\"type\":\"long\",\"nullable\":true,\"metadata\":{}},{\"name\":\"MAX_COP_PROCESS_ADDRESS\",\"type\":\"string\",\"nullable\":true,\"metadata\":{}},{\"name\":\"MAX_COP_PROCESS_TIME\",\"type\":\"long\",\"nullable\":true,\"metadata\":{}},{\"name\":\"MAX_COP_WAIT_ADDRESS\",\"type\":\"string\",\"nullable\":true,\"metadata\":{}},{\"name\":\"MAX_COP_WAIT_TIME\",\"type\":\"long\",\"nullable\":true,\"metadata\":{}},{\"name\":\"MAX_DISK\",\"type\":\"long\",\"nullable\":true,\"metadata\":{}},{\"name\":\"MAX_GET_COMMIT_TS_TIME\",\"type\":\"long\",\"nullable\":true,\"metadata\":{}},{\"name\":\"MAX_LATENCY\",\"type\":\"long\",\"nullable\":true,\"metadata\":{}},{\"name\":\"MAX_LOCAL_LATCH_WAIT_TIME\",\"type\":\"long\",\"nullable\":true,\"metadata\":{}},{\"name\":\"MAX_MEM\",\"type\":\"long\",\"nullable\":true,\"metadata\":{}},{\"name\":\"MAX_PARSE_LATENCY\",\"type\":\"long\",\"nullable\":true,\"metadata\":{}},{\"name\":\"MAX_PREWRITE_REGIONS\",\"type\":\"integer\",\"nullable\":true,\"metadata\":{}},{\"name\":\"MAX_PREWRITE_TIME\",\"type\":\"long\",\"nullable\":true,\"metadata\":{}},{\"name\":\"MAX_PROCESSED_KEYS\",\"type\":\"long\",\"nullable\":true,\"metadata\":{}},{\"name\":\"MAX_PROCESS_TIME\",\"type\":\"long\",\"nullable\":true,\"metadata\":{}},{\"name\":\"MAX_QUEUED_RC_TIME\",\"type\":\"long\",\"nullable\":true,\"metadata\":{}},{\"name\":\"MAX_REQUEST_UNIT_READ\",\"type\":\"double\",\"nullable\":true,\"metadata\":{}},{\"name\":\"MAX_REQUEST_UNIT_WRITE\",\"type\":\"double\",\"nullable\":true,\"metadata\":{}},{\"name\":\"MAX_RESOLVE_LOCK_TIME\",\"type\":\"long\",\"nullable\":true,\"metadata\":{}},{\"name\":\"MAX_RESULT_ROWS\",\"type\":\"long\",\"nullable\":true,\"metadata\":{}},{\"name\":\"MAX_ROCKSDB_BLOCK_CACHE_HIT_COUNT\",\"type\":\"integer\",\"nullable\":true,\"metadata\":{}},{\"name\":\"MAX_ROCKSDB_BLOCK_READ_BYTE\",\"type\":\"integer\",\"nullable\":true,\"metadata\":{}},{\"name\":\"MAX_ROCKSDB_BLOCK_READ_COUNT\",\"type\":\"integer\",\"nullable\":true,\"metadata\":{}},{\"name\":\"MAX_ROCKSDB_DELETE_SKIPPED_COUNT\",\"type\":\"integer\",\"nullable\":true,\"metadata\":{}},{\"name\":\"MAX_ROCKSDB_KEY_SKIPPED_COUNT\",\"type\":\"integer\",\"nullable\":true,\"metadata\":{}},{\"name\":\"MAX_TOTAL_KEYS\",\"type\":\"long\",\"nullable\":true,\"metadata\":{}},{\"name\":\"MAX_TXN_RETRY\",\"type\":\"integer\",\"nullable\":true,\"metadata\":{}},{\"name\":\"MAX_WAIT_TIME\",\"type\":\"long\",\"nullable\":true,\"metadata\":{}},{\"name\":\"MAX_WRITE_KEYS\",\"type\":\"long\",\"nullable\":true,\"metadata\":{}},{\"name\":\"MAX_WRITE_SIZE\",\"type\":\"long\",\"nullable\":true,\"metadata\":{}},{\"name\":\"MIN_LATENCY\",\"type\":\"long\",\"nullable\":true,\"metadata\":{}},{\"name\":\"MIN_RESULT_ROWS\",\"type\":\"long\",\"nullable\":true,\"metadata\":{}},{\"name\":\"PLAN\",\"type\":\"string\",\"nullable\":true,\"metadata\":{}},{\"name\":\"PLAN_CACHE_HITS\",\"type\":\"long\",\"nullable\":true,\"metadata\":{}},{\"name\":\"PLAN_CACHE_UNQUALIFIED\",\"type\":\"long\",\"nullable\":true,\"metadata\":{}},{\"name\":\"PLAN_CACHE_UNQUALIFIED_LAST_REASON\",\"type\":\"string\",\"nullable\":true,\"metadata\":{}},{\"name\":\"PLAN_DIGEST\",\"type\":\"string\",\"nullable\":true,\"metadata\":{}},{\"name\":\"PLAN_HINT\",\"type\":\"string\",\"nullable\":true,\"metadata\":{}},{\"name\":\"PLAN_IN_BINDING\",\"type\":\"boolean\",\"nullable\":true,\"metadata\":{}},{\"name\":\"PLAN_IN_CACHE\",\"type\":\"boolean\",\"nullable\":true,\"metadata\":{}},{\"name\":\"PREPARED\",\"type\":\"boolean\",\"nullable\":true,\"metadata\":{}},{\"name\":\"PREV_SAMPLE_TEXT\",\"type\":\"string\",\"nullable\":true,\"metadata\":{}},{\"name\":\"QUERY_SAMPLE_TEXT\",\"type\":\"string\",\"nullable\":true,\"metadata\":{}},{\"name\":\"RESOURCE_GROUP\",\"type\":\"string\",\"nullable\":true,\"metadata\":{}},{\"name\":\"SAMPLE_USER\",\"type\":\"string\",\"nullable\":true,\"metadata\":{}},{\"name\":\"SCHEMA_NAME\",\"type\":\"string\",\"nullable\":true,\"metadata\":{}},{\"name\":\"STMT_TYPE\",\"type\":\"string\",\"nullable\":true,\"metadata\":{}},{\"name\":\"STORAGE_KV\",\"type\":\"boolean\",\"nullable\":true,\"metadata\":{}},{\"name\":\"STORAGE_MPP\",\"type\":\"boolean\",\"nullable\":true,\"metadata\":{}},{\"name\":\"SUMMARY_BEGIN_TIME\",\"type\":\"timestamp_ntz\",\"nullable\":true,\"metadata\":{}},{\"name\":\"SUMMARY_END_TIME\",\"type\":\"timestamp_ntz\",\"nullable\":true,\"metadata\":{}},{\"name\":\"SUM_BACKOFF_TIMES\",\"type\":\"long\",\"nullable\":true,\"metadata\":{}},{\"name\":\"SUM_COP_TASK_NUM\",\"type\":\"long\",\"nullable\":true,\"metadata\":{}},{\"name\":\"SUM_ERRORS\",\"type\":\"integer\",\"nullable\":true,\"metadata\":{}},{\"name\":\"SUM_EXEC_RETRY\",\"type\":\"long\",\"nullable\":true,\"metadata\":{}},{\"name\":\"SUM_EXEC_RETRY_TIME\",\"type\":\"long\",\"nullable\":true,\"metadata\":{}},{\"name\":\"SUM_LATENCY\",\"type\":\"long\",\"nullable\":true,\"metadata\":{}},{\"name\":\"SUM_UNPACKED_BYTES_RECEIVED_TIFLASH_CROSS_ZONE\",\"type\":\"long\",\"nullable\":true,\"metadata\":{}},{\"name\":\"SUM_UNPACKED_BYTES_RECEIVED_TIFLASH_TOTAL\",\"type\":\"long\",\"nullable\":true,\"metadata\":{}},{\"name\":\"SUM_UNPACKED_BYTES_RECEIVED_TIKV_CROSS_ZONE\",\"type\":\"long\",\"nullable\":true,\"metadata\":{}},{\"name\":\"SUM_UNPACKED_BYTES_RECEIVED_TIKV_TOTAL\",\"type\":\"long\",\"nullable\":true,\"metadata\":{}},{\"name\":\"SUM_UNPACKED_BYTES_SENT_TIFLASH_CROSS_ZONE\",\"type\":\"long\",\"nullable\":true,\"metadata\":{}},{\"name\":\"SUM_UNPACKED_BYTES_SENT_TIFLASH_TOTAL\",\"type\":\"long\",\"nullable\":true,\"metadata\":{}},{\"name\":\"SUM_UNPACKED_BYTES_SENT_TIKV_CROSS_ZONE\",\"type\":\"long\",\"nullable\":true,\"metadata\":{}},{\"name\":\"SUM_UNPACKED_BYTES_SENT_TIKV_TOTAL\",\"type\":\"long\",\"nullable\":true,\"metadata\":{}},{\"name\":\"SUM_WARNINGS\",\"type\":\"integer\",\"nullable\":true,\"metadata\":{}},{\"name\":\"TABLE_NAMES\",\"type\":\"string\",\"nullable\":true,\"metadata\":{}}]}","partitionColumns":[],"createdTime":1756280540183,"configuration":{}}}
{"add":{"path":"part-00001-ba84e65c-5cb0-4a55-9cd2-88ef66ca149c-c000.snappy.parquet","partitionValues":{},"size":252800,"modificationTime":1756280540228,"dataChange":true,"stats":"{\"numRecords\":275,\"minValues\":{\"AVG_ROCKSDB_BLOCK_CACHE_HIT_COUNT\":-0.0,\"AVG_PREWRITE_TIME\":0,\"AVG_GET_COMMIT_TS_TIME\":0,\"_vector_instance\":\"127.0.0.1:4000\",\"_vector_source_schema\":\"information_schema\",\"AVG_AFFECTED_ROWS\":-0.0,\"AVG_BACKOFF_TIME\":0,\"AVG_ROCKSDB_BLOCK_READ_COUNT\":-0.0,\"AVG_COMMIT_TIME\":0,\"_vector_source_table\":\"CLUSTER_STATEMENTS_SUMMARY\",\"AVG_RESOLVE_LOCK_TIME\":0,\"AVG_DISK\":0,\"AVG_PROCESSED_KEYS\":0,\"AVG_LOCAL_LATCH_WAIT_TIME\":0,\"AVG_PREWRITE_REGIONS\":-0.0,\"AVG_PROCESS_TIME\":0,\"_vector_timestamp\":\"2025-08-27T07:41:20.059437+00:00\",\"AVG_QUEUED_RC_TIME\":0,\"AVG_KV_TIME\":0,\"AVG_BACKOFF_TOTAL_TIME\":0,\"AVG_COMMIT_BACKOFF_TIME\":0,\"AVG_REQUEST_UNIT_READ\":-0.0,\"AVG_RESULT_ROWS\":0,\"AVG_MEM\":0,\"AVG_LATENCY\":28303,\"AVG_ROCKSDB_BLOCK_READ_BYTE\":-0.0,\"AVG_ROCKSDB_DELETE_SKIPPED_COUNT\":-0.0,\"_vector_table\":\"hist_cluster_statements_summary\",\"AVG_COMPILE_LATENCY\":7556,\"AVG_PD_TIME\":0,\"AVG_PARSE_LATENCY\":0,\"AVG_REQUEST_UNIT_WRITE\":-0.0},\"maxValues\":{\"AVG_RESOLVE_LOCK_TIME\":0,\"_vector_table\":\"hist_cluster_statements_summary\",\"AVG_AFFECTED_ROWS\":2500.0,\"AVG_ROCKSDB_BLOCK_CACHE_HIT_COUNT\":2.5e-323,\"AVG_ROCKSDB_DELETE_SKIPPED_COUNT\":4.94e-322,\"AVG_LATENCY\":3785955042,\"AVG_RESULT_ROWS\":137,\"AVG_PARSE_LATENCY\":5285864,\"AVG_ROCKSDB_BLOCK_READ_BYTE\":0.0,\"AVG_ROCKSDB_BLOCK_READ_COUNT\":0.0,\"AVG_GET_COMMIT_TS_TIME\":107812,\"AVG_COMMIT_BACKOFF_TIME\":0,\"AVG_PREWRITE_TIME\":13473687,\"AVG_REQUEST_UNIT_READ\":10.197604370117187,\"AVG_REQUEST_UNIT_WRITE\":754.6829589843754,\"AVG_BACKOFF_TIME\":0,\"AVG_BACKOFF_TOTAL_TIME\":0,\"AVG_QUEUED_RC_TIME\":0,\"_vector_source_schema\":\"information_schema\",\"_vector_source_table\":\"CLUSTER_STATEMENTS_SUMMARY\",\"AVG_PROCESSED_KEYS\":100,\"AVG_DISK\":0,\"AVG_LOCAL_LATCH_WAIT_TIME\":0,\"AVG_PD_TIME\":171885,\"AVG_KV_TIME\":345862209,\"AVG_PREWRITE_REGIONS\":4.607341719942256,\"AVG_PROCESS_TIME\":169498,\"_vector_timestamp\":\"2025-08-27T07:42:20.114650+00:00\",\"AVG_COMMIT_TIME\":7017416,\"_vector_instance\":\"127.0.0.1:4000\",\"AVG_COMPILE_LATENCY\":8159406,\"AVG_MEM\":2189000},\"nullCount\":{\"AVG_LOCAL_LATCH_WAIT_TIME\":0,\"AVG_PROCESS_TIME\":0,\"AVG_DISK\":0,\"AVG_COMMIT_BACKOFF_TIME\":0,\"AVG_ROCKSDB_DELETE_SKIPPED_COUNT\":0,\"AVG_BACKOFF_TIME\":0,\"_vector_table\":0,\"AVG_REQUEST_UNIT_WRITE\":0,\"AVG_RESULT_ROWS\":0,\"AVG_RESOLVE_LOCK_TIME\":0,\"AVG_ROCKSDB_BLOCK_CACHE_HIT_COUNT\":0,\"AVG_ROCKSDB_BLOCK_READ_COUNT\":0,\"AVG_PARSE_LATENCY\":0,\"_vector_instance\":0,\"_vector_timestamp\":0,\"AVG_REQUEST_UNIT_READ\":0,\"AVG_COMMIT_TIME\":0,\"AVG_PREWRITE_REGIONS\":0,\"_vector_source_table\":0,\"AVG_LATENCY\":0,\"AVG_PROCESSED_KEYS\":0,\"AVG_ROCKSDB_BLOCK_READ_BYTE\":0,\"AVG_GET_COMMIT_TS_TIME\":0,\"AVG_AFFECTED_ROWS\":0,\"AVG_MEM\":0,\"AVG_COMPILE_LATENCY\":0,\"AVG_PD_TIME\":0,\"AVG_KV_TIME\":0,\"AVG_PREWRITE_TIME\":0,\"AVG_BACKOFF_TOTAL_TIME\":0,\"_vector_source_schema\":0,\"AVG_QUEUED_RC_TIME\":0}}","tags":null,"baseRowId":null,"defaultRowCommitVersion":null,"clusteringProvider":null}}
{"commitInfo":{"timestamp":1756280540229,"operation":"WRITE","operationParameters":{"mode":"Append"},"engineInfo":"delta-rs:0.27.0","operationMetrics":{"num_added_files":1,"num_removed_files":0,"num_partitions":0,"num_added_rows":275,"execution_time_ms":46},"clientVersion":"delta-rs.0.27.0"}}