mod fetch;

use std::fmt;

pub use fetch::{Fetch<PERSON><PERSON><PERSON>, TopologyFetcher};

#[derive(Debug, <PERSON><PERSON>, <PERSON><PERSON>, Eq, Hash, PartialEq)]
pub enum InstanceType {
    PD,
    TiDB,
    TiKV,
    TiFlash,
    TiProxy,
    Lightning,
}

impl fmt::Display for InstanceType {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        match self {
            InstanceType::PD => write!(f, "pd"),
            InstanceType::TiDB => write!(f, "tidb"),
            InstanceType::TiKV => write!(f, "tikv"),
            InstanceType::TiFlash => write!(f, "tiflash"),
            InstanceType::TiProxy => write!(f, "tiproxy"),
            InstanceType::Lightning => write!(f, "lightning"),
        }
    }
}

#[derive(Debug, <PERSON>lone, Eq, Hash, <PERSON>ialEq)]
pub struct Component {
    pub instance_type: InstanceType,
    pub host: String,
    pub primary_port: u16,
    pub secondary_port: u16,
}

impl Component {
    pub fn conprof_address(&self) -> Option<String> {
        match self.instance_type {
            InstanceType::PD => Some(format!("{}:{}", self.host, self.primary_port)),
            InstanceType::TiDB
            | InstanceType::TiKV
            | InstanceType::TiFlash
            | InstanceType::TiProxy
            | InstanceType::Lightning => Some(format!("{}:{}", self.host, self.secondary_port)),
        }
    }
}

impl fmt::Display for Component {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        write!(
            f,
            "{}({}:{}, {}:{})",
            self.instance_type, self.host, self.primary_port, self.host, self.secondary_port
        )
    }
}
