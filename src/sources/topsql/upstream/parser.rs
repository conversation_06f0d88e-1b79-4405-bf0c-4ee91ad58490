use chrono::{DateTime, Utc};
use std::sync::Arc;
use vector_lib::event::LogEvent;

use crate::sources::topsql::schema_cache::SchemaCache;
use crate::sources::topsql::upstream::{
    consts::{
        LABEL_DB_NAME, LABEL_INSTANCE, LABEL_INSTANCE_TYPE, LABEL_NAME, LABEL_PLAN_DIGEST,
        LABEL_SQL_DIGEST, LABEL_TABLE_ID, LABEL_TABLE_NAME, LABEL_TAG_LABEL,
    },
    utils::make_metric_like_log_event,
};

pub trait UpstreamEventParser {
    type UpstreamEvent;

    fn parse(
        event: Self::UpstreamEvent,
        instance: String,
        schema_cache: Arc<SchemaCache>,
    ) -> Vec<LogEvent>;

    fn keep_top_n(responses: Vec<Self::UpstreamEvent>, top_n: usize) -> Vec<Self::UpstreamEvent>;

    fn downsampling(responses: &mut Vec<Self::UpstreamEvent>, interval_sec: u32);
}

pub struct Buf {
    labels: Vec<(&'static str, String)>,
    timestamps: Vec<DateTime<Utc>>,
    values: Vec<f64>,
}

impl Default for Buf {
    fn default() -> Self {
        Self {
            labels: vec![
                (LABEL_NAME, String::new()),
                (LABEL_INSTANCE, String::new()),
                (LABEL_INSTANCE_TYPE, String::new()),
                (LABEL_SQL_DIGEST, String::new()),
                (LABEL_PLAN_DIGEST, String::new()),
                (LABEL_TAG_LABEL, String::new()),
                (LABEL_DB_NAME, String::new()),
                (LABEL_TABLE_NAME, String::new()),
                (LABEL_TABLE_ID, String::new()),
            ],
            timestamps: vec![],
            values: vec![],
        }
    }
}

impl Buf {
    pub fn label_name(&mut self, label_name: impl Into<String>) -> &mut Self {
        self.labels[0].1 = label_name.into();
        self
    }

    pub fn instance(&mut self, instance: impl Into<String>) -> &mut Self {
        self.labels[1].1 = instance.into();
        self
    }

    pub fn instance_type(&mut self, instance_type: impl Into<String>) -> &mut Self {
        self.labels[2].1 = instance_type.into();
        self
    }

    pub fn sql_digest(&mut self, sql_digest: impl Into<String>) -> &mut Self {
        self.labels[3].1 = sql_digest.into();
        self
    }

    pub fn plan_digest(&mut self, plan_digest: impl Into<String>) -> &mut Self {
        self.labels[4].1 = plan_digest.into();
        self
    }

    pub fn tag_label(&mut self, tag_label: impl Into<String>) -> &mut Self {
        self.labels[5].1 = tag_label.into();
        self
    }

    pub fn db_name(&mut self, db_name: impl Into<String>) -> &mut Self {
        self.labels[6].1 = db_name.into();
        self
    }

    pub fn table_name(&mut self, table_name: impl Into<String>) -> &mut Self {
        self.labels[7].1 = table_name.into();
        self
    }

    pub fn table_id(&mut self, table_id: impl Into<String>) -> &mut Self {
        self.labels[8].1 = table_id.into();
        self
    }

    pub fn points(&mut self, points: impl Iterator<Item = (u64, f64)>) -> &mut Self {
        for (timestamp_sec, value) in points {
            self.timestamps.push(
                DateTime::from_timestamp(timestamp_sec as i64, 0)
                    .expect("invalid or out-of-range datetime"),
            );
            self.values.push(value);
        }
        self
    }

    pub fn build_event(&mut self) -> Option<LogEvent> {
        let res = if self.timestamps.is_empty() || self.values.is_empty() {
            None
        } else {
            Some(make_metric_like_log_event(
                &self.labels,
                &self.timestamps,
                &self.values,
            ))
        };

        self.timestamps.clear();
        self.values.clear();
        res
    }
}
